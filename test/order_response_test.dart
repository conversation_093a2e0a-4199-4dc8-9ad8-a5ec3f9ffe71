import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Order Response Handling Tests', () {
    test('should handle error response correctly', () {
      // Simulate the new error response structure
      final errorResponse = {"error": true, "message": "Balance not enough"};
      
      expect(errorResponse["error"], true);
      expect(errorResponse["message"], "Balance not enough");
    });

    test('should handle success response correctly', () {
      // Simulate the new success response structure
      final successResponse = {"error": false, "payment_url": "https://payment.example.com"};
      
      expect(successResponse["error"], false);
      expect(successResponse["payment_url"], "https://payment.example.com");
    });

    test('should handle null response correctly', () {
      // Simulate null response
      final nullResponse = null;
      
      expect(nullResponse, null);
    });

    test('should handle legacy string response correctly', () {
      // Simulate legacy string response for backward compatibility
      final legacyResponse = "Balance not enough";

      expect(legacyResponse, isA<String>());
      expect(legacyResponse, "Balance not enough");
    });

    // Ticket Payment Tests
    test('should handle ticket payment error response correctly', () {
      final errorResponse = {"error": true, "message": "Balance not enough"};

      expect(errorResponse["error"], true);
      expect(errorResponse["message"], "Balance not enough");
    });

    test('should handle ticket payment success message correctly', () {
      final successResponse = {"error": false, "message": "Muvaffaqiyatli to'landi"};

      expect(successResponse["error"], false);
      expect(successResponse["message"], "Muvaffaqiyatli to'landi");
    });

    test('should handle ticket payment success with data correctly', () {
      final successResponse = {
        "error": false,
        "data": {
          "order_id": 123,
          "payment_url": "https://payment.example.com"
        }
      };

      expect(successResponse["error"], false);
      final data = successResponse["data"] as Map<String, dynamic>;
      expect(data["order_id"], 123);
      expect(data["payment_url"], "https://payment.example.com");
    });

    test('should handle active ticket error correctly', () {
      final errorResponse = {"error": true, "message": "Sizda ushbu o'yin uchun faol chipta bor!"};

      expect(errorResponse["error"], true);
      expect(errorResponse["message"], "Sizda ushbu o'yin uchun faol chipta bor!");
    });
  });
}
