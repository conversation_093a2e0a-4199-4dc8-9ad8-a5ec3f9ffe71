# 📱 Push Notifications Setup Guide

Bu qo'llanma Echipta ilovasiga push notifications qo'shish uchun to'liq yo'riqnoma.

## ✅ **O'rnatilgan Komponentlar**

### **1. Dependencies**
```yaml
# pubspec.yaml
firebase_core: ^3.6.0
firebase_messaging: ^15.1.3
flutter_local_notifications: ^18.0.1
```

### **2. <PERSON><PERSON><PERSON><PERSON>**
- `lib/core/services/push_notification_service.dart` - Asosiy push notification service
- `lib/core/widgets/notification_widget.dart` - In-app notification widget
- `lib/features/notifications/presentation/notification_test_screen.dart` - Test screen
- `android/app/src/main/res/values/colors.xml` - Android notification colors

### **3. Konfiguratsiya <PERSON>i**
- `android/app/google-services.json` ✅ (mavjud)
- `ios/Runner/GoogleService-Info.plist` ✅ (mavjud)
- `android/app/src/main/AndroidManifest.xml` ✅ (yangilandi)

## 🚀 **Is<PERSON>ga Tushirish**

### **1. Dependencies O'rnatish**
```bash
flutter pub get
```

### **2. iOS Konfiguratsiyasi**
iOS uchun qo'shimcha sozlamalar kerak:

1. **Xcode da ochish:**
   ```bash
   open ios/Runner.xcworkspace
   ```

2. **Capabilities qo'shish:**
   - Target > Runner > Signing & Capabilities
   - "+ Capability" tugmasini bosing
   - "Push Notifications" ni qo'shing
   - "Background Modes" ni qo'shing va "Background fetch" va "Remote notifications" ni belgilang

3. **Info.plist yangilash:**
   `ios/Runner/Info.plist` fayliga qo'shing:
   ```xml
   <key>UIBackgroundModes</key>
   <array>
       <string>fetch</string>
       <string>remote-notification</string>
   </array>
   ```

### **3. Android Konfiguratsiyasi**
Android konfiguratsiyasi avtomatik bajarildi:
- ✅ Permissions qo'shildi
- ✅ Firebase service qo'shildi
- ✅ Notification channel sozlandi

## 🧪 **Test Qilish**

### **1. Test Screen Ochish**
Test screen ga o'tish uchun:
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const NotificationTestScreen(),
  ),
);
```

### **2. FCM Token Olish**
1. Test screen da FCM token ni ko'ring
2. Token ni nusxalang
3. Firebase Console da test notification yuboring

### **3. Firebase Console Test**
1. [Firebase Console](https://console.firebase.google.com/) ga kiring
2. Loyihangizni tanlang
3. Cloud Messaging > Send your first message
4. FCM token ni kiriting va test xabar yuboring

## 📋 **Notification Types**

### **1. Push Notifications (FCM)**
- Server dan keluvchi notifications
- App yopiq bo'lganda ham ishlaydi
- Background va foreground handling

### **2. Local Notifications**
- Ilovadan o'zi ko'rsatadigan notifications
- Timer, reminder lar uchun

### **3. In-App Notifications**
- Ilova ichida ko'rsatiladigan notifications
- Custom design va animation

## 🔧 **Foydalanish**

### **1. Push Notification Service**
```dart
// Initialize
await PushNotificationService().initialize(context);

// Get FCM token
String? token = PushNotificationService().fcmToken;

// Send test notification
await PushNotificationService().sendTestNotification();
```

### **2. In-App Notifications**
```dart
// Success notification
NotificationHelper.showSuccess(
  context,
  title: 'Muvaffaqiyat!',
  message: 'Amal muvaffaqiyatli bajarildi',
);

// Error notification
NotificationHelper.showError(
  context,
  title: 'Xatolik!',
  message: 'Nimadir noto\'g\'ri ketdi',
);

// Info notification
NotificationHelper.showInfo(
  context,
  title: 'Ma\'lumot',
  message: 'Yangi ma\'lumot mavjud',
);
```

### **3. Navigation Handling**
Notification data asosida navigation:
```json
{
  "type": "ticket",
  "route": "/tickets",
  "id": "123"
}
```

## 🎯 **Notification Data Format**

### **Server dan yuborilishi kerak bo'lgan format:**
```json
{
  "notification": {
    "title": "Chipta sotib olindi",
    "body": "Sizning chiptangiz tayyor!"
  },
  "data": {
    "type": "ticket",
    "route": "/tickets",
    "id": "123",
    "click_action": "FLUTTER_NOTIFICATION_CLICK"
  },
  "to": "FCM_TOKEN_HERE"
}
```

### **Navigation Types:**
- `ticket` - Chipta sahifasiga o'tish
- `order` - Order status sahifasiga o'tish
- `match` - Match sahifasiga o'tish
- `custom` - Custom route

## 🔐 **Security**

### **1. FCM Token Security**
- FCM token ni server da xavfsiz saqlang
- Token refresh ni handle qiling
- User logout qilganda token ni o'chiring

### **2. Data Validation**
- Notification data ni validate qiling
- Malicious navigation dan himoyalaning

## 📱 **Platform Specific**

### **Android**
- ✅ Notification channels
- ✅ Custom notification icon
- ✅ Notification color
- ✅ Vibration va sound

### **iOS**
- ⚠️ Qo'shimcha Xcode sozlamalari kerak
- ⚠️ Push notification certificate kerak
- ⚠️ Provisioning profile yangilash kerak

## 🐛 **Troubleshooting**

### **1. Token null bo'lsa:**
- Internet connection ni tekshiring
- Firebase konfiguratsiyasini tekshiring
- App ni restart qiling

### **2. Notification kelmasa:**
- FCM token to'g'ri ekanligini tekshiring
- Firebase Console da message history ni ko'ring
- Device notification settings ni tekshiring

### **3. Navigation ishlamasa:**
- Notification data format ni tekshiring
- Route lar to'g'ri ekanligini tekshiring
- Context mavjudligini tekshiring

## 📚 **Qo'shimcha Ma'lumotlar**

- [Firebase Cloud Messaging](https://firebase.google.com/docs/cloud-messaging)
- [Flutter Local Notifications](https://pub.dev/packages/flutter_local_notifications)
- [Firebase Flutter Setup](https://firebase.flutter.dev/docs/messaging/overview)

## ✨ **Keyingi Qadamlar**

1. **Server Integration**: Backend bilan FCM token almashish
2. **User Preferences**: Notification settings sahifasi
3. **Analytics**: Notification click tracking
4. **Rich Notifications**: Image, action buttons qo'shish
5. **Scheduled Notifications**: Vaqt bo'yicha notification yuborish
