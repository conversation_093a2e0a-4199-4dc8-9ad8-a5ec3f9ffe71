import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  double _opacity = 0.0;

  @override
  void initState() {
    super.initState();
    // Start the animation after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _opacity = 1.0;
      });
    });
    checkLanguage();
  }

  void checkLanguage() async {
    if (StorageRepository.getString(StoreKeys.language).isEmpty) {
      await StorageRepository.putString(StoreKeys.language, "uz");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.maxFinite,
        height: double.maxFinite,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [AppColors.primary, AppColors.primary.withOpacity(0.9)],
          ),
        ),
        alignment: Alignment.center,
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return AnimatedOpacity(
                onEnd: () {
                  final pincode =
                      StorageRepository.getString(StoreKeys.pincode);
                  if (state.authStatus == AuthenticationStatus.authenticated) {
                    if (pincode.isNotEmpty) {
                      context.go(AppRouter.pincode);
                    } else {
                      context.go(AppRouter.setPincode);
                    }
                  } else {
                    context.go(AppRouter.language);
                  }
                },
                opacity: _opacity,
                duration: const Duration(seconds: 2),
                child: SvgPicture.asset(
                  AppAssets.whiteLogo,
                  width: 150,
                ));
          },
        ),
      ),
    );
  }
}
