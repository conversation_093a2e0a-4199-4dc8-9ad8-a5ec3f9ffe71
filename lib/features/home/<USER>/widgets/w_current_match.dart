import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/core/utils/size_config.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_stadium.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';

class WCurrentMatch extends StatelessWidget {
  const WCurrentMatch({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.gameStatus.isInProgress) {
          return const SizedBox(
            width: double.maxFinite,
            height: 200,
            child: Center(
              child: CircularProgressIndicator.adaptive(
                backgroundColor: AppColors.white,
              ),
            ),
          );
        } else if (state.gameStatus.isSuccess) {
          final item = state.game.currentMatch;
          if (item == null) {
            return Center(
              child: Text(
                "currentMatchNotAvailable".tr(),
                style: context.textTheme.displayMedium!.copyWith(
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                  color: AppColors.white,
                ),
              ),
            );
          }
          return Column(
            children: [
              Center(
                child: Text(
                  LocaleKeys.currentMatch.tr(),
                  style: context.textTheme.displayMedium!.copyWith(
                    color: AppColors.white,
                    fontSize: 28,
                  ),
                ),
              ),
              const Gap(20),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: GestureDetector(
                  onTap: () {
                    context.read<OrderBloc>().add(
                      const SelectOrderTypeEvent(orderType: OrderType.ticket),
                    );
                    context.read<OrderBloc>().add(
                      SelectMatchEvent(match: item),
                    );
                    context.push(AppRouter.sector, extra: item);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: AppColors.white,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              item.title,
                              style: context.textTheme.displaySmall,
                            ),
                          ],
                        ),
                        const Gap(12),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CachedNetworkImage(
                              imageUrl: item.main_team.image,
                              width: wi(60),
                              height: he(60),
                              errorWidget: (context, url, error) {
                                return Icon(Icons.sports_soccer, size: 70);
                              },
                            ),
                            // const Gap(10),
                            Column(
                              children: [
                                if (item.match_status == "match_finished") ...[
                                  Text(
                                    "${item.team1_score}:${item.team2_score}",
                                    style: context.textTheme.displayMedium!
                                        .copyWith(
                                          fontSize: 42,
                                          fontWeight: FontWeight.w700,
                                          color: AppColors.primary,
                                        ),
                                  ),
                                ] else ...[
                                  Text(
                                    DateFormat(
                                      'HH:mm',
                                    ).format(item.start_date!),
                                    style: context.textTheme.displayMedium!
                                        .copyWith(
                                          fontSize: 42,
                                          fontWeight: FontWeight.w700,
                                          color: AppColors.primary,
                                        ),
                                  ),
                                ],
                                const Gap(4),
                                Text(
                                  DateFormat("dd-MMMM", context.locale.languageCode).format(item.start_date!),
                                  style: context.textTheme.bodyLarge!.copyWith(),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    showModalBottomSheet(
                                      context: context,
                                      showDragHandle: true,
                                      isScrollControlled: true,
                                      useRootNavigator: true,
                                      elevation: 0,
                                      builder: (context) {
                                        final stadium = item.match_stadium;
                                        return WStadium(stadium: stadium);
                                      },
                                    );
                                  },
                                  child: Row(
                                    children: [
                                      const Icon(
                                        CupertinoIcons.location_solid,
                                        color: AppColors.primary,
                                      ),
                                      Text(
                                        item.match_stadium.name,
                                        style: context.textTheme.displaySmall!
                                            .copyWith(color: AppColors.primary),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            // const Gap(10),
                            CachedNetworkImage(
                              imageUrl: item.second_team.image,
                              width: wi(60),
                              height: he(60),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }
}
