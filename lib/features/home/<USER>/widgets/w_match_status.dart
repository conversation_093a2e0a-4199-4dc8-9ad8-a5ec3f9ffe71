
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
class WMatchStatus extends StatelessWidget {
  const WMatchStatus({
    super.key,
    required this.item,
  });

  final MatchEntity item;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100),
        color:  item.match_status == "match_finished"
            ? AppColors.red3
            : item.match_status == "match_not_started"
                ? AppColors.primary3
                : AppColors.green3
      ),
      child: Text(
       ( item.match_status == "match_finished"
            ? LocaleKeys.finished.tr()
            : item.match_status == "match_not_started"
                ? LocaleKeys.onSale.tr()
                : LocaleKeys.started.tr()).toUpperCase(),
        style: context.textTheme.bodySmall!.copyWith(
          fontSize: 8,
          color: item.match_status == "match_finished"
            ? AppColors.red
            : item.match_status == "match_not_started"
                ? AppColors.primary
                : AppColors.green
        ),
      ),
    );
  }
}
