import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/home/<USER>/widgets/w_bottom_of_appbar.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class WHomeAppBar extends StatelessWidget {
  const WHomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        return SliverAppBar(
          pinned: true,
          backgroundColor: AppColors.primary,
          elevation: 0,
          surfaceTintColor: AppColors.primary.withOpacity(1),
          leading: BlocBuilder<ProfileBloc, ProfileState>(
            builder: (context, state) {
              final me = state.me;
              return GestureDetector(
                onTap: () {
                  context.push(AppRouter.team).then((value) {
                    context.read<ProfileBloc>().add(GetMeEvent());
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: CachedNetworkImage(
                    imageUrl: me.team.image,
                    width: 30,
                    progressIndicatorBuilder: (context, url, progress) {
                      return const SizedBox();
                    },
                    errorWidget: (context, url, error) {
                      return SvgPicture.asset(AppAssets.team, color: AppColors.white);
                    },
                  ),
                ),
              );
            },
          ),
          leadingWidth: 80,
          actions: [
            IconButton(
              onPressed: () {
                context.push(AppRouter.notifications);
              },
              icon: SvgPicture.asset(AppAssets.bell, width: 32, color: AppColors.white),
            ),
            // IconButton(
            //   onPressed: () {
            //     showSearch(context: context, delegate: SearchScreen());
            //   },
            //   icon: SvgPicture.asset(
            //     AppAssets.search,
            //     width: 32,
            //     color: AppColors.white,
            //   ),
            // ),
            Gap(8),
          ],
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(bottom: Radius.circular(32))),
          expandedHeight: 320,
          flexibleSpace: const FlexibleSpaceBar(background: WBottomOFAppBar()),
        );
      },
    );
  }
}
