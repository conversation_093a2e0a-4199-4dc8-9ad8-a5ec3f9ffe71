import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:lottie/lottie.dart';
import 'package:url_launcher/url_launcher_string.dart';

void showUpdateDialog(BuildContext context, {required bool required, String? description}) {
  showModalBottomSheet(
    context: context,
    useRootNavigator: true,
    isDismissible: !required,
    enableDrag: !required,
    isScrollControlled: true,
    useSafeArea: true,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(12))),
    builder: (context) => InAppUpdateDialog(required: required, description: description),
  );
}

class InAppUpdateDialog extends StatefulWidget {
  final bool required;
  final String? description;

  const InAppUpdateDialog({super.key, required this.required, this.description});

  @override
  State<InAppUpdateDialog> createState() => _InAppUpdateDialogState();
}

class _InAppUpdateDialogState extends State<InAppUpdateDialog> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Lottie.asset('assets/animations/confetti.json', width: double.infinity),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Lottie.asset('assets/animations/update.json', height: 240),
              Gap(4),
              Text(
                widget.required ? "updateTitleRequired".tr() : "updateTitleOptional".tr(),
                style: TextStyle(fontSize: 22),
                textAlign: TextAlign.center,
              ),
              Gap(8),
              Text(
                widget.description ?? (widget.required ? "updateDescRequired".tr() : "updateDescOptional".tr()),
                style: context.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              Gap(12),
              Row(
                children: [
                  if (!widget.required) ...[
                    Expanded(
                      child: WButton(
                        hasBorder: true,
                        onTap: () {
                          Navigator.pop(context);
                        },
                        txt: "cancelUpdate".tr(),
                      ),
                    ),
                    Gap(8),
                  ],
                  Expanded(
                    child: WButton(
                      txt: "update".tr(),
                      onTap: () async {
                        try {
                          if (Platform.isAndroid || Platform.isIOS) {
                            final String url =
                                Platform.isIOS
                                    ? 'https://apps.apple.com/uz/app/texnomart/id6741811156'
                                    : 'https://play.google.com/store/apps/details?id=uz.echipta.app&hl=ru&gl=US';

                            launchUrlString(url, mode: LaunchMode.externalApplication);
                          }
                        } catch (e, s) {
                          debugPrint('$s');
                        }
                      },
                    ),
                  ),
                ],
              ),
              Gap(32),
            ],
          ),
        ),
      ],
    );
  }
}
