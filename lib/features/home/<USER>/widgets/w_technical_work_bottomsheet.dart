import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:flutter/material.dart';

import 'dart:io';

import 'package:gap/gap.dart';
import 'package:lottie/lottie.dart';

void showTechnicalWorkDialog(BuildContext context) {
  showModalBottomSheet(
    context: context,
    useRootNavigator: true,
    isDismissible: false,
    enableDrag: false,
    isScrollControlled: true,
    builder: (context) {
      return TechnicalWork();
    },
  );
}

class TechnicalWork extends StatelessWidget {
  const TechnicalWork({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Gap(16),
        Lottie.asset('assets/animations/technical_work.json', width: 250, height: 250, fit: BoxFit.cover),
        Gap(16),
        Text("technicalWorksTitle".tr(), style: TextStyle(fontSize: 22), textAlign: TextAlign.center),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text("technicalWorksDesc".tr(), style: context.textTheme.bodyLarge, textAlign: TextAlign.center),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: WButton(
            onTap: () {
              exit(0);
            },
            txt: "closeApp".tr(),
          ),
        ),
        Gap(40),
      ],
    );
  }
}
