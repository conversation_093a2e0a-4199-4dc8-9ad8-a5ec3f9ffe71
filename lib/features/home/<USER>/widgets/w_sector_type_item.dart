import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/order/domain/entities/sector_type_entity.dart';
import 'package:flutter/material.dart';

class WSectorTypeItem extends StatelessWidget {
  const WSectorTypeItem({super.key, required this.type});
  final SectorTypeEntity type;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: type.color,
        borderRadius: BorderRadius.circular(100),
      ),
      child: Text(
        context.locale.languageCode == "uz" ? type.title : type.titleRu,
        style: context.textTheme.labelMedium!.copyWith(color: AppColors.white),
      ),
    );
  }
}
