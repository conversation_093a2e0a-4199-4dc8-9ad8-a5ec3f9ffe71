import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class WHomeError extends StatelessWidget {
  const WHomeError({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error,
              color: AppColors.red,
              size: 100,
            ),
            const Gap(20),
            Text(
              LocaleKeys.somethingWentWrong.tr(),
              textAlign: TextAlign.center,
              style: context.textTheme.displayLarge,
            ),
            const Gap(20),
            IconButton.filled(
              onPressed: () {
                context.read<ProfileBloc>().add(GetMeEvent());
                context.read<HomeBloc>().add(GetGameEvent());
              },
              style: IconButton.styleFrom(backgroundColor: AppColors.primary),
              icon: const Icon(Icons.replay_outlined),
            )
          ],
        ),
      ),
    );
  }
}
