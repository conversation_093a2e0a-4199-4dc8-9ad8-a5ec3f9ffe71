import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/home/<USER>/widgets/w_current_match.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class WBottomOFAppBar extends StatelessWidget {
  const WBottomOFAppBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.only(
              bottom: 20,
              top: context.padding.top + AppBar().preferredSize.height + 16),
          child: const WCurrentMatch(),
        );
      },
    );
  }
}
