
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
class WSectorRowSeat extends StatelessWidget {
  const WSectorRowSeat({
    super.key,
    required this.sector,
    required this.row,
    required this.seat,
  });

  final String sector;
  final String row;
  final String seat;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 83,
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              Text(
                LocaleKeys.sector.tr(),
                style: context.textTheme.bodySmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                sector,
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
          const Gap(16),
          const VerticalDivider(
            thickness: 2,
            color: AppColors.darkGrey,
          ),
          const Gap(16),
          Column(
            children: [
              Text(
                LocaleKeys.row.tr(),
                style: context.textTheme.bodySmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                row,
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
          const Gap(16),
          const VerticalDivider(
            thickness: 2,
            color: AppColors.darkGrey,
          ),
          const Gap(16),
          Column(
            children: [
              Text(
                LocaleKeys.emptyPlaces.tr(),
                style: context.textTheme.bodySmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                seat,
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
        ],
      ),
    );
  }
}
