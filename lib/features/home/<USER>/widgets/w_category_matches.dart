import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/home/<USER>/widgets/w_game_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';

class WGames extends StatelessWidget {
  const WGames({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.gameStatus.isInProgress) {
          return const SliverFillRemaining(child: Center(child: CircularProgressIndicator.adaptive()));
        } else if (state.gameStatus.isSuccess) {
          return SliverList(
            delegate: SliverChildListDelegate([
              // Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     children: List.generate(
              //       state.matchCategories.length,
              //       (index) {
              //         final item = state.matchCategories[index];
              //         return Expanded(
              //           child: GestureDetector(
              //             onTap: () {
              //               // context
              //               //     .read<HomeBloc>()
              //               //     .add(SelectMatchCategoryEvent(item));
              //             },
              //             child: Container(
              //               padding: const EdgeInsets.all(8),
              //               margin:
              //                   EdgeInsets.only(right: index == 0 ? 8 : 0),
              //               alignment: Alignment.center,
              //               decoration: BoxDecoration(
              //                   borderRadius: BorderRadius.circular(18),
              //                   color: state.selectedMatchCategory == item
              //                       ? AppColors.primary2
              //                       : AppColors.white),
              //               child: Row(
              //                 children: [
              //                   CachedNetworkImage(
              //                     imageUrl: item.image,
              //                     width: 30,
              //                   ),
              //                   const Gap(4),
              //                   Expanded(
              //                       child: Text(
              //                     item.title,
              //                     textAlign: TextAlign.center,
              //                     style: context.textTheme.bodySmall!
              //                         .copyWith(height: 1),
              //                   ))
              //                 ],
              //               ),
              //             ),
              //           ),
              //         );
              //       },
              //     )),
              // const Gap(20),
              Container(
                margin: const EdgeInsets.only(bottom: 20),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: state.game.categoryMatches.length,
                  separatorBuilder: (context, index) => const Gap(20),
                  itemBuilder: (context, index) {
                    final item = state.game.categoryMatches[index];
                    return WGameItem(item: item);
                  },
                ),
              ),
            ]),
          );
        } else {
          return const SliverToBoxAdapter(child: SizedBox());
        }
      },
    );
  }
}
