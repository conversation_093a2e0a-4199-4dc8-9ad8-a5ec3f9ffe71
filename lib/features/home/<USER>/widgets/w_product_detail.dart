import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';

void showProductDetail(BuildContext context, ProductEntity item) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    showDragHandle: true,
    elevation: 0,
    useRootNavigator: true,
    backgroundColor: AppColors.white,
    builder: (context) {
      return BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          return Container(
            padding: const EdgeInsets.all(20),
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: CachedNetworkImage(
                    imageUrl: item.image,
                    height: 254,
                    width: 254,
                    fit: BoxFit.cover,
                  ),
                ),
                const Gap(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      context.locale.languageCode == "uz"
                          ? item.title_uz
                          : item.title_ru,
                      style: Theme.of(context).textTheme.displayLarge,
                    ),
                    const Gap(20),
                    Text(
                      LocaleKeys.price.tr(
                        namedArgs: {
                          "price":
                              item.price.toDouble().formatAsSpaceSeparated(),
                        },
                      ),
                      style: Theme.of(
                        context,
                      ).textTheme.displaySmall!.copyWith(color: AppColors.red),
                    ),
                  ],
                ),
                const Gap(22),
                BlocBuilder<OrderBloc, OrderState>(
                  builder: (context, state) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.read<OrderBloc>().add(
                                SelectProductDeliveryTypeEvent(
                                  ProductDeliveryType.in_store,
                                ),
                              );
                            },
                            child: Container(
                              height: 50,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color:
                                    state.productDeliveryType ==
                                            ProductDeliveryType.in_store
                                        ? AppColors.green
                                        : AppColors.mediumGrey,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    AppAssets.store,
                                    width: 30,
                                    color:
                                        state.productDeliveryType ==
                                                ProductDeliveryType.in_store
                                            ? AppColors.white
                                            : AppColors.black,
                                  ),
                                  Gap(4),
                                  Text(
                                    "Olib ketish",
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          state.productDeliveryType ==
                                                  ProductDeliveryType.in_store
                                              ? AppColors.white
                                              : AppColors.black,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Gap(10),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.read<OrderBloc>().add(
                                SelectProductDeliveryTypeEvent(
                                  ProductDeliveryType.delivery,
                                ),
                              );
                            },
                            child: Container(
                              height: 50,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color:
                                    state.productDeliveryType ==
                                            ProductDeliveryType.delivery
                                        ? AppColors.green
                                        : AppColors.mediumGrey,
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    AppAssets.delivery,
                                    width: 30,
                                    color:
                                        state.productDeliveryType ==
                                                ProductDeliveryType.delivery
                                            ? AppColors.white
                                            : AppColors.black,
                                  ),
                                  Gap(4),
                                  Text(
                                    "Yetkazib berish",
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          state.productDeliveryType ==
                                                  ProductDeliveryType.delivery
                                              ? AppColors.white
                                              : AppColors.black,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                const Gap(10),
                if (item.sizes != null) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: List.generate((item.sizes as List).length, (
                      index,
                    ) {
                      final size = item.sizes[index];
                      return GestureDetector(
                        onTap: () {
                          context.read<OrderBloc>().add(
                            SelectSizeOfProductEvent(size),
                          );
                        },
                        child: BlocBuilder<OrderBloc, OrderState>(
                          builder: (context, state) {
                            return Container(
                              width: 67,
                              height: 50,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color:
                                    state.selectedSizeOfProduct == size
                                        ? AppColors.green
                                        : AppColors.mediumGrey,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                size,
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color:
                                      state.selectedSizeOfProduct == size
                                          ? AppColors.white
                                          : AppColors.black,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    }),
                  ),
                ],
                const Gap(30),
                Text(
                  LocaleKeys.deliveryAddress.tr(),
                  style: TextStyle(
                    color: AppColors.darkGrey,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                const Gap(30),
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.commonPrice.tr(),
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppColors.darkGrey,
                          ),
                        ),
                        Text(
                          LocaleKeys.price.tr(
                            namedArgs: {
                              "price":
                                  item.price
                                      .toDouble()
                                      .formatAsSpaceSeparated(),
                            },
                          ),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                    const Gap(20),
                    Expanded(
                      child: WButton(
                        // btnColor: AppColors.primary,
                        txt: LocaleKeys.pay.tr(),
                        onTap: () {
                          
                          // context.read<OrderBloc>().add(PaymentProductEvent(
                          //       items: [Item(product_id: item.id, count: 1)],
                          //       delivery_type: state.bringType.name,
                          //       delivery_address: "3-sektor 4-qator 5-joy",
                          //       payment_type: "alif",
                          //       onError: (p0) {},
                          //       onSuccess: (p0) {},
                          //     ));
                        },
                      ),
                    ),
                  ],
                ),
                Gap(MediaQuery.of(context).viewPadding.bottom),
              ],
            ),
          );
        },
      );
    },
  );
}
