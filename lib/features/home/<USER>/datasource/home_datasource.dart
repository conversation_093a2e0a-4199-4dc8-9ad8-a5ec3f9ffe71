import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/auth/data/models/item_model.dart';
import 'package:echipta/features/home/<USER>/models/app_version_model.dart';
import 'package:echipta/features/home/<USER>/models/game_model.dart';
import 'package:echipta/features/home/<USER>/models/news_model.dart';
import 'package:echipta/features/home/<USER>/models/product_model.dart';

abstract class HomeDatasource {
  Future<List<ItemModel>> getMatchCategories();
  Future<GameModel> getGame();
  Future<List<ProductModel>> getProducts();
  Future<List<NewsModel>> getNews();
  Future<AppVersionModel> getAppVersion();
}

class HomeDatasourceImpl implements HomeDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  @override
  Future<List<ItemModel>> getMatchCategories() async {
    try {
      final response = await _dio.get("/games/get-match-categories");
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List).map((e) => ItemModel.fromJson(e as Map<String, dynamic>)).toList();
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<GameModel> getGame() async {
    try {
      final response = await _dio.get("/games/get-games");
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return GameModel.fromJson(response.data["data"]);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<List<ProductModel>> getProducts() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/products/get-products", options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List).map((e) => ProductModel.fromJson(e as Map<String, dynamic>)).toList();
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<List<NewsModel>> getNews() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/games/news", options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List).map((e) => NewsModel.fromJson(e as Map<String, dynamic>)).toList();
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<AppVersionModel> getAppVersion() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/settings/version", options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return AppVersionModel.fromJson(response.data["data"] as Map<String, dynamic>);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
