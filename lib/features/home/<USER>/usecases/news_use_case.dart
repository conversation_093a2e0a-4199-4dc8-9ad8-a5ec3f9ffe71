import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/home/<USER>/repository/home_repository_impl.dart';
import 'package:echipta/features/home/<USER>/entities/news_entity.dart';
import 'package:echipta/features/home/<USER>/repository/home_repostiory.dart';

class NewsUseCase extends UseCase<List<NewsEntity>, NoParams> {
  final HomeRepostiory _repostiory = serviceLocator<HomeRepositoryImpl>();

  @override
  Future<Either<Failure, List<NewsEntity>>> call(NoParams params) async {
    return await _repostiory.getNews();
  }
}
