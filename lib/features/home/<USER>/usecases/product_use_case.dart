import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/home/<USER>/repository/home_repository_impl.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:echipta/features/home/<USER>/repository/home_repostiory.dart';

class ProductUseCase extends UseCase<List<ProductEntity>, NoParams> {
  final HomeRepostiory _repostiory = serviceLocator<HomeRepositoryImpl>();

  @override
  Future<Either<Failure, List<ProductEntity>>> call(NoParams params) async {
    return await _repostiory.getProducts();
  }
}
