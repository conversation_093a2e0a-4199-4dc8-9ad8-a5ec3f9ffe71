// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MatchModel _$MatchModelFromJson(Map<String, dynamic> json) => MatchModel(
  id: (json['id'] as num?)?.toInt() ?? 0,
  main_team:
      json['main_team'] == null
          ? const ItemEntity()
          : const ItemConverter().fromJson(
            json['main_team'] as Map<String, dynamic>,
          ),
  match_stadium:
      json['match_stadium'] == null
          ? const MatchStadiumEntity()
          : const MatchStadiumConverter().fromJson(
            json['match_stadium'] as Map<String, dynamic>,
          ),
  match_status: json['match_status'] as String? ?? "",
  second_team:
      json['second_team'] == null
          ? const ItemEntity()
          : const ItemConverter().fromJson(
            json['second_team'] as Map<String, dynamic>,
          ),
  start_date:
      json['start_date'] == null
          ? null
          : DateTime.parse(json['start_date'] as String),
  team1_score: (json['team1_score'] as num?)?.toInt() ?? 0,
  team2_score: (json['team2_score'] as num?)?.toInt() ?? 0,
  title: json['title'] as String? ?? "",
);

Map<String, dynamic> _$MatchModelToJson(
  MatchModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'start_date': instance.start_date?.toIso8601String(),
  'main_team': const ItemConverter().toJson(instance.main_team),
  'second_team': const ItemConverter().toJson(instance.second_team),
  'team1_score': instance.team1_score,
  'team2_score': instance.team2_score,
  'match_status': instance.match_status,
  'match_stadium': const MatchStadiumConverter().toJson(instance.match_stadium),
};
