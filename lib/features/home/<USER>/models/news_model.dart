import 'package:echipta/features/home/<USER>/entities/news_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'news_model.g.dart';

@JsonSerializable()
class NewsModel extends NewsEntity {
  const NewsModel({
    super.datetime,
    super.id,
    super.image,
    super.text,
    super.title,
  });

  factory NewsModel.fromJson(Map<String, dynamic> json) =>
      _$NewsModelFromJson(json);
}
