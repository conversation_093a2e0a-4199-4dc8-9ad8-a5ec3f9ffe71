import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/entities/game_entity.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'game_model.g.dart';

@JsonSerializable()
class GameModel extends GameEntity {
  const GameModel({
    super.categoryMatches,
    super.currentMatch,
  });
  factory GameModel.fromJson(Map<String, dynamic> json) =>
      _$GameModelFromJson(json);
}
