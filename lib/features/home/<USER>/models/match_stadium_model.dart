import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/entities/match_stadium_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'match_stadium_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class MatchStadiumModel extends MatchStadiumEntity {
  const MatchStadiumModel({
    super.address,
    super.count_sectors,
    super.id,
    super.images,
    super.latitude,
    super.longitude,
    super.name,
  });

  factory MatchStadiumModel.fromJson(Map<String, dynamic> json) =>
      _$MatchStadiumModelFromJson(json);
}
