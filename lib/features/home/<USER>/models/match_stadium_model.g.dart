// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_stadium_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MatchStadiumModel _$MatchStadiumModelFromJson(Map<String, dynamic> json) =>
    MatchStadiumModel(
      address: json['address'] as String? ?? "",
      count_sectors: (json['count_sectors'] as num?)?.toInt() ?? 0,
      id: (json['id'] as num?)?.toInt() ?? 0,
      images:
          (json['images'] as List<dynamic>?)
              ?.map(
                (e) =>
                    const ItemConverter().fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
      latitude: json['latitude'] as String? ?? "",
      longitude: json['longitude'] as String? ?? "",
      name: json['name'] as String? ?? "",
    );

Map<String, dynamic> _$MatchStadiumModelToJson(MatchStadiumModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'count_sectors': instance.count_sectors,
      'address': instance.address,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'images': instance.images.map(const ItemConverter().toJson).toList(),
    };
