// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/home/<USER>/entities/ticket_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'ticket_model.g.dart';

@JsonSerializable()
class TicketModel extends TicketEntity {
  const TicketModel({
    // super.ticket_id,
    super.sector,
    super.row,
    super.seat,
    // super.match,
    super.type,
    super.price,
    super.panorama,
  });

  factory TicketModel.fromJson(Map<String, dynamic> json) =>
      _$TicketModelFromJson(json);
}
