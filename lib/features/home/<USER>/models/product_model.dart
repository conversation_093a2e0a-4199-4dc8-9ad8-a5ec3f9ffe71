// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'product_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class ProductModel extends ProductEntity {
  const ProductModel({
    super.id,
    super.title_uz,
    super.title_ru,
    super.description_uz,
    super.description_ru,
    super.price,
    super.image,
    super.status,
    super.sizes,
    super.delivery_price,
    super.ikpu,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);
}
