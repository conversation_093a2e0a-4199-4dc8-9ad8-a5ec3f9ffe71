import 'package:echipta/features/home/<USER>/entities/app_version_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'app_version_model.g.dart';

@JsonSerializable()
class AppVersionModel extends AppVersionEntity {
  const AppVersionModel({super.version, super.status, super.required, super.message});

  factory AppVersionModel.fromJson(Map<String, dynamic> json) => _$AppVersionModelFromJson(json);
}
