import 'package:echipta/features/home/<USER>/entities/sector_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'sector_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class SectorModel extends SectorEntity {
  const SectorModel({
    super.free_seats_count,
    super.price,
    super.title,
    super.type,
  });

  factory SectorModel.fromJson(Map<String, dynamic> json) =>
      _$SectorModelFromJson(json);
}
