import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/home/<USER>/entities/match_stadium_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'match_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class MatchModel extends MatchEntity {
  const MatchModel({
    super.id,
    super.main_team,
    super.match_stadium,
    super.match_status,
    super.second_team,
    super.start_date,
    super.team1_score,
    super.team2_score,
    super.title,
  });

  factory MatchModel.fromJson(Map<String, dynamic> json) =>
      _$MatchModelFromJson(json);
}
