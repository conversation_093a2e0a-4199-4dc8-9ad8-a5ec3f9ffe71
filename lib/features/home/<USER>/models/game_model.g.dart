// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'game_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GameModel _$GameModelFromJson(Map<String, dynamic> json) => GameModel(
  categoryMatches:
      (json['categoryMatches'] as List<dynamic>?)
          ?.map(
            (e) => const ItemConverter().fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
  currentMatch: _$JsonConverterFromJson<Map<String, dynamic>, MatchEntity>(
    json['currentMatch'],
    const MatchConverter().fromJson,
  ),
);

Map<String, dynamic> _$GameModelToJson(GameModel instance) => <String, dynamic>{
  'currentMatch': _$JsonConverterToJson<Map<String, dynamic>, MatchEntity>(
    instance.currentMatch,
    const MatchConverter().toJson,
  ),
  'categoryMatches':
      instance.categoryMatches.map(const ItemConverter().toJson).toList(),
};

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) => json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) => value == null ? null : toJson(value);
