// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

class AppVersionEntity extends Equatable {
  final int version;
  final bool status;
  final bool required;
  final String message;

  const AppVersionEntity({this.version = 0, this.status = false, this.required = false, this.message = ''});
  @override
  List<Object?> get props => [version, status, required, message];

  AppVersionEntity copyWith({int? version, bool? status, bool? required, String? message}) {
    return AppVersionEntity(
      version: version ?? this.version,
      status: status ?? this.status,
      required: required ?? this.required,
      message: message ?? this.message,
    );
  }
}
