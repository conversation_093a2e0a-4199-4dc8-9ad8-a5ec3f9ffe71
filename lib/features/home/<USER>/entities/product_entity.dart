// ignore_for_file: public_member_api_docs, sort_constructors_first, non_constant_identifier_names
import 'package:echipta/features/home/<USER>/models/product_model.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

class ProductEntity extends Equatable {
  final int id;
  final String title_uz;
  final String title_ru;
  final String description_uz;
  final String description_ru;
  final int price;
  final String image;
  final int status;
  final dynamic sizes;
  final int delivery_price;
  final String ikpu;
  const ProductEntity({
    this.id = 0,
    this.title_uz = "",
    this.title_ru = "",
    this.description_uz = "",
    this.description_ru = "",
    this.price = 0,
    this.image = "",
    this.status = 0,
    this.sizes,
    this.delivery_price = 0,
    this.ikpu = "",
  });

  @override
  List<Object?> get props => [
        id,
        title_uz,
        title_ru,
        description_uz,
        description_ru,
        price,
        image,
        status,
        sizes,
        delivery_price,
        ikpu,
      ];

  ProductEntity copyWith({
    int? id,
    String? title_uz,
    String? title_ru,
    String? description_uz,
    String? description_ru,
    int? price,
    String? image,
    int? status,
    dynamic sizes,
    int? delivery_price,
    String? ikpu,
  }) {
    return ProductEntity(
      id: id ?? this.id,
      title_uz: title_uz ?? this.title_uz,
      title_ru: title_ru ?? this.title_ru,
      description_uz: description_uz ?? this.description_uz,
      description_ru: description_ru ?? this.description_ru,
      price: price ?? this.price,
      image: image ?? this.image,
      status: status ?? this.status,
      sizes: sizes ?? this.sizes,
      delivery_price: delivery_price ?? this.delivery_price,
      ikpu: ikpu ?? this.ikpu,
    );
  }
}

class ProductConverter
    extends JsonConverter<ProductEntity, Map<String, dynamic>> {
  @override
  ProductEntity fromJson(Map<String, dynamic> json) =>
      ProductModel.fromJson(json);

  @override
  Map<String, dynamic> toJson(ProductEntity object) => {};

  const ProductConverter();
}
