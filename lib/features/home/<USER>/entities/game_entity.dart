// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';

class GameEntity extends Equatable {
  @MatchConverter()
  final MatchEntity? currentMatch;
  @ItemConverter()
  final List<ItemEntity> categoryMatches;

  const GameEntity({
    this.currentMatch,
    this.categoryMatches = const [],
  });

  @override
  List<Object?> get props => [
        currentMatch,
        categoryMatches,
      ];

  GameEntity copyWith({
    MatchEntity? currentMatch,
    List<ItemEntity>? categoryMatches,
  }) {
    return GameEntity(
      currentMatch: currentMatch ?? this.currentMatch,
      categoryMatches: categoryMatches ?? this.categoryMatches,
    );
  }
}
