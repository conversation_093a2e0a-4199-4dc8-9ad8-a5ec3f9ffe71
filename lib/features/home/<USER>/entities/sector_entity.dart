// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class SectorEntity extends Equatable {
  final String title;
  final String type;
  final String price;
  final int free_seats_count;

  const SectorEntity({
    this.title = "",
    this.type = "",
    this.price = "",
    this.free_seats_count = 0,
  });
  @override
  List<Object?> get props => [
        title,
        type,
        price,
        free_seats_count,
      ];

  SectorEntity copyWith({
    String? title,
    String? type,
    String? price,
    int? free_seats_count,
  }) {
    return SectorEntity(
      title: title ?? this.title,
      type: type ?? this.type,
      price: price ?? this.price,
      free_seats_count: free_seats_count ?? this.free_seats_count,
    );
  }
}
