// ignore_for_file: public_member_api_docs, sort_constructors_first, non_constant_identifier_names
import 'package:equatable/equatable.dart';

class TicketEntity extends Equatable {
  // final int ticket_id;
  final String sector;
  final String row;
  final String seat;
  // @MatchConverter()
  // final MatchEntity match;
  final String type;
  final String price;
  final String panorama;
  final String start_date;
  const TicketEntity({
    // this.ticket_id = 0,
    this.sector = "",
    this.row = "",
    this.seat = "",
    // this.match = const MatchEntity(),
    this.type = "",
    this.price = "",
    this.panorama = "",
    this.start_date = "",
  });

  @override
  List<Object?> get props => [
        // ticket_id,
        sector,
        row,
        seat,
        // match,
        type,
        price,
        panorama,
        start_date,
      ];

  TicketEntity copyWith({
    String? sector,
    String? row,
    String? seat,
    String? type,
    String? price,
    String? panorama,
    String? start_date,
  }) {
    return TicketEntity(
      sector: sector ?? this.sector,
      row: row ?? this.row,
      seat: seat ?? this.seat,
      type: type ?? this.type,
      price: price ?? this.price,
      panorama: panorama ?? this.panorama,
      start_date: start_date ?? this.start_date,
    );
  }
}
