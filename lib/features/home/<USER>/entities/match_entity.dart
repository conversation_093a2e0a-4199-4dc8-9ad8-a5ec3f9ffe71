// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/models/match_model.dart';
import 'package:echipta/features/home/<USER>/entities/match_stadium_entity.dart';

class MatchEntity extends Equatable {
  final int id;
  final String title;
  final DateTime? start_date;
  @ItemConverter()
  final ItemEntity main_team;
  @ItemConverter()
  final ItemEntity second_team;
  final int team1_score;
  final int team2_score;
  final String match_status;
  @MatchStadiumConverter()
  final MatchStadiumEntity match_stadium;

  const MatchEntity({
    this.id = 0,
    this.title = "",
    this.start_date,
    this.main_team = const ItemEntity(),
    this.second_team = const ItemEntity(),
    this.team1_score = 0,
    this.team2_score = 0,
    this.match_status = "",
    this.match_stadium = const MatchStadiumEntity(),
  });
  @override
  List<Object?> get props => [
        id,
        title,
        start_date,
        main_team,
        second_team,
        team1_score,
        team2_score,
        match_stadium,
        match_status,
      ];

  MatchEntity copyWith({
    int? id,
    String? title,
    DateTime? start_date,
    ItemEntity? main_team,
    ItemEntity? second_team,
    int? team1_score,
    int? team2_score,
    String? match_status,
    MatchStadiumEntity? match_stadium,
  }) {
    return MatchEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      start_date: start_date ?? this.start_date,
      main_team: main_team ?? this.main_team,
      second_team: second_team ?? this.second_team,
      team1_score: team1_score ?? this.team1_score,
      team2_score: team2_score ?? this.team2_score,
      match_status: match_status ?? this.match_status,
      match_stadium: match_stadium ?? this.match_stadium,
    );
  }
}

class MatchConverter extends JsonConverter<MatchEntity, Map<String, dynamic>> {
  @override
  MatchEntity fromJson(Map<String, dynamic> json) => MatchModel.fromJson(json);

  @override
  Map<String, dynamic> toJson(MatchEntity object) => {};

  const MatchConverter();
}
