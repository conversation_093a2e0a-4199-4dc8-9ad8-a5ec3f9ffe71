// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/models/match_stadium_model.dart';

class MatchStadiumEntity extends Equatable {
  final int id;
  final String name;
  final int count_sectors;
  final String address;
  final String longitude;
  final String latitude;
  @ItemConverter()
  final List<ItemEntity> images;

  const MatchStadiumEntity({
    this.id = 0,
    this.name = "",
    this.count_sectors = 0,
    this.address = "",
    this.longitude = "",
    this.latitude = "",
    this.images = const [],
  });

  @override
  List<Object?> get props => [
    id,
    name,
    count_sectors,
    address,
    longitude,
    latitude,
    images,
  ];

  MatchStadiumEntity copyWith({
    int? id,
    String? name,
    int? count_sectors,
    String? address,
    String? longitude,
    String? latitude,
    List<ItemEntity>? images,
  }) {
    return MatchStadiumEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      count_sectors: count_sectors ?? this.count_sectors,
      address: address ?? this.address,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      images: images ?? this.images,
    );
  }
}

class MatchStadiumConverter
    extends JsonConverter<MatchStadiumEntity, Map<String, dynamic>> {
  @override
  MatchStadiumEntity fromJson(Map<String, dynamic> json) =>
      MatchStadiumModel.fromJson(json);

  @override
  Map<String, dynamic> toJson(MatchStadiumEntity object) => {};

  const MatchStadiumConverter();
}
