// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

class NewsEntity extends Equatable {
  final int id;
  final String title;
  final String image;
  final String text;
  final String datetime;

  const NewsEntity({
    this.id = 0,
    this.title = "",
    this.image = "",
    this.text = "",
    this.datetime = "",
  });
  @override
  List<Object?> get props => [id, title, image, text, datetime];

  NewsEntity copyWith({
    int? id,
    String? title,
    String? image,
    String? text,
    String? datetime,
  }) {
    return NewsEntity(
      id: id ?? this.id,
      title: title ?? this.title,
      image: image ?? this.image,
      text: text ?? this.text,
      datetime: datetime ?? this.datetime,
    );
  }
}
