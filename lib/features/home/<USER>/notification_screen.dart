import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/common/widgets/w_error.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';

class NotificationScreen extends StatelessWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(LocaleKeys.notification.tr(), style: context.textTheme.displaySmall)),
      body: Bloc<PERSON>uilder<HomeBloc, HomeState>(
        builder: (context, state) {
          if (state.newsStatus.isInProgress) {
            return Center(child: CircularProgressIndicator.adaptive());
          } else if (state.newsStatus.isFailure) {
            return WError();
          } else if (state.newsStatus.isSuccess && state.news.isEmpty) {
            return WEmptyScreen();
          }
          return ListView.separated(
            padding: EdgeInsets.all(20),
            separatorBuilder: (context, index) => Gap(10),
            itemCount: state.news.length,
            itemBuilder: (context, index) {
              final item = state.news[index];
              return Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(21),
                  color: AppColors.white,
                  boxShadow: [
                    BoxShadow(
                      offset: Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                      color: AppColors.black.withOpacity(0.25),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 168,
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(21),
                        image: DecorationImage(image: CachedNetworkImageProvider(item.image)),
                      ),
                    ),
                    Gap(10),
                    Text(item.title, style: context.textTheme.bodyLarge),
                    Text(item.text, maxLines: 3, overflow: TextOverflow.ellipsis),
                    Gap(10),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Text(
                        DateFormat(
                          "dd MMMM yyyy",
                          context.locale.languageCode,
                        ).format(DateTime.tryParse(item.datetime) ?? DateTime.now()),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
