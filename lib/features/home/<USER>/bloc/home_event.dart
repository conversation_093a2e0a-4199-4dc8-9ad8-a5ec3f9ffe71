part of 'home_bloc.dart';

sealed class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

class GetMatchCategoriesEvent extends HomeEvent {}

class ShowSearchEvent extends HomeEvent {}

class SelectMatchCategoryEvent extends HomeEvent {
  final ItemEntity matchCategory;
  const SelectMatchCategoryEvent(this.matchCategory);
}

class RemoveMatchCategoryEvent extends HomeEvent {
  final ItemEntity matchCategory;
  const RemoveMatchCategoryEvent(this.matchCategory);
}

class GetGameEvent extends HomeEvent {}

class GetProductsEvent extends HomeEvent {}

class GetNewsEvent extends HomeEvent {}
class GetAppVersionEvent extends HomeEvent {}
