import 'package:bloc/bloc.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/enums.dart';
import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/entities/game_entity.dart';
import 'package:echipta/features/home/<USER>/entities/news_entity.dart';
import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:echipta/features/home/<USER>/entities/ticket_entity.dart';
import 'package:echipta/features/home/<USER>/usecases/game_use_case.dart';
import 'package:echipta/features/home/<USER>/usecases/get_app_version_use_case.dart';
import 'package:echipta/features/home/<USER>/usecases/match_categories_use_case.dart';
import 'package:echipta/features/home/<USER>/usecases/news_use_case.dart';
import 'package:echipta/features/home/<USER>/usecases/product_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:package_info_plus/package_info_plus.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final MatchCategoriesUseCase _matchCategoriesUseCase = MatchCategoriesUseCase();
  final GameUseCase _gameUseCase = GameUseCase();
  final ProductUseCase _productUseCase = ProductUseCase();
  final NewsUseCase _newsUseCase = NewsUseCase();
  final GetAppVersionUseCase _appVersionUseCase = GetAppVersionUseCase();
  HomeBloc() : super(HomeInitial()) {
    on<GetMatchCategoriesEvent>((event, emit) async {
      emit(state.copyWith(matchCategoriesStatus: FormzSubmissionStatus.inProgress));
      final result = await _matchCategoriesUseCase.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(matchCategoriesStatus: FormzSubmissionStatus.success, matchCategories: result.right));
      } else {
        emit(state.copyWith(matchCategoriesStatus: FormzSubmissionStatus.failure));
      }
    });
    on<ShowSearchEvent>((event, emit) => emit(state.copyWith(showSearch: !state.showSearch)));
    on<SelectMatchCategoryEvent>((event, emit) {
      final matchCategory = event.matchCategory;
      List<ItemEntity> updatedSelectedMatchCategories = List.from(state.selectedMatchCategory)..add(matchCategory);
      emit(state.copyWith(selectedMatchCategory: updatedSelectedMatchCategories));
    });
    on<RemoveMatchCategoryEvent>((event, emit) {
      final matchCategory = event.matchCategory;
      List<ItemEntity> updatedSelectedMatchCategories = List.from(state.selectedMatchCategory)..remove(matchCategory);
      emit(state.copyWith(selectedMatchCategory: updatedSelectedMatchCategories));
    });

    on<GetGameEvent>((event, emit) async {
      emit(state.copyWith(gameStatus: FormzSubmissionStatus.inProgress));
      final result = await _gameUseCase.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(gameStatus: FormzSubmissionStatus.success, game: result.right));
      } else {
        emit(state.copyWith(gameStatus: FormzSubmissionStatus.failure));
      }
    });
    on<GetProductsEvent>((event, emit) async {
      emit(state.copyWith(productsStatus: FormzSubmissionStatus.inProgress));
      final result = await _productUseCase.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(products: result.right, productsStatus: FormzSubmissionStatus.success));
      } else {
        emit(state.copyWith(productsStatus: FormzSubmissionStatus.failure));
      }
    });
    on<GetNewsEvent>((event, emit) async {
      emit(state.copyWith(newsStatus: FormzSubmissionStatus.inProgress));
      final result = await _newsUseCase.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(news: result.right, newsStatus: FormzSubmissionStatus.success));
      } else {
        emit(state.copyWith(newsStatus: FormzSubmissionStatus.failure));
      }
    });
    on<GetAppVersionEvent>((event, emit) async {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      double buildNumber = double.tryParse(packageInfo.buildNumber) ?? 0;
      final result = await _appVersionUseCase.call(NoParams());
      if (result.isRight) {
        if (!result.right.status) {
          // App is not working
          emit(state.copyWith(appUpdateStatus: AppUpdateStatus.notWorking));
        } else if (result.right.version > buildNumber) {
          // App is working, a new version available
          if (result.right.required) {
            // New version is required
            emit(state.copyWith(appUpdateStatus: AppUpdateStatus.required));
          } else {
            // New version is optional
            emit(state.copyWith(appUpdateStatus: AppUpdateStatus.optional));
          }
        } else {
          // There isn't a new version
          emit(state.copyWith(appUpdateStatus: AppUpdateStatus.none));
        }
      } else {
        add(event);
      }
    });
  }
}
