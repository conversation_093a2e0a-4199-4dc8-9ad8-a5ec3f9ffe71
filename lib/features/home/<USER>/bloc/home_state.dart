// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'home_bloc.dart';

class HomeState extends Equatable {
  final List<ItemEntity> matchCategories;
  final FormzSubmissionStatus matchCategoriesStatus;
  final List<ItemEntity> selectedMatchCategory;
  final bool showSearch;
  final GameEntity game;
  final FormzSubmissionStatus gameStatus;
  final List<ProductEntity> products;
  final FormzSubmissionStatus productsStatus;
  final List<TicketEntity> myTickets;
  final FormzSubmissionStatus myTicketsStatus;
  final List<NewsEntity> news;
  final FormzSubmissionStatus newsStatus;
  final AppUpdateStatus appUpdateStatus;
  const HomeState({
    this.matchCategories = const [],
    this.matchCategoriesStatus = FormzSubmissionStatus.initial,
    this.selectedMatchCategory = const [],
    this.showSearch = false,
    this.game = const GameEntity(),
    this.gameStatus = FormzSubmissionStatus.initial,
    this.products = const [],
    this.productsStatus = FormzSubmissionStatus.initial,
    this.myTickets = const [],
    this.myTicketsStatus = FormzSubmissionStatus.initial,
    this.news = const [],
    this.newsStatus = FormzSubmissionStatus.initial,
    this.appUpdateStatus = AppUpdateStatus.none,
  });

  @override
  List<Object> get props => [
    matchCategories,
    matchCategoriesStatus,
    selectedMatchCategory,
    showSearch,
    game,
    gameStatus,
    products,
    productsStatus,
    myTickets,
    myTicketsStatus,
    news,
    newsStatus,
    appUpdateStatus,
  ];

  HomeState copyWith({
    List<ItemEntity>? matchCategories,
    FormzSubmissionStatus? matchCategoriesStatus,
    List<ItemEntity>? selectedMatchCategory,
    bool? showSearch,
    GameEntity? game,
    FormzSubmissionStatus? gameStatus,
    List<ProductEntity>? products,
    FormzSubmissionStatus? productsStatus,
    List<TicketEntity>? myTickets,
    FormzSubmissionStatus? myTicketsStatus,
    List<NewsEntity>? news,
    FormzSubmissionStatus? newsStatus,
    AppUpdateStatus? appUpdateStatus,
  }) {
    return HomeState(
      matchCategories: matchCategories ?? this.matchCategories,
      matchCategoriesStatus: matchCategoriesStatus ?? this.matchCategoriesStatus,
      selectedMatchCategory: selectedMatchCategory ?? this.selectedMatchCategory,
      showSearch: showSearch ?? this.showSearch,
      game: game ?? this.game,
      gameStatus: gameStatus ?? this.gameStatus,
      products: products ?? this.products,
      productsStatus: productsStatus ?? this.productsStatus,
      myTickets: myTickets ?? this.myTickets,
      myTicketsStatus: myTicketsStatus ?? this.myTicketsStatus,
      news: news ?? this.news,
      newsStatus: newsStatus ?? this.newsStatus,
      appUpdateStatus: appUpdateStatus ?? this.appUpdateStatus,
    );
  }
}

final class HomeInitial extends HomeState {}
