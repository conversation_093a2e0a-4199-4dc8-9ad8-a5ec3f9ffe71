import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/enums.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/home/<USER>/widgets/w_category_matches.dart';
import 'package:echipta/features/home/<USER>/widgets/w_home_appbar.dart';
import 'package:echipta/features/home/<USER>/widgets/w_products.dart';
import 'package:echipta/features/home/<USER>/widgets/w_technical_work_bottomsheet.dart';
import 'package:echipta/features/home/<USER>/widgets/w_update_bottomsheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final RefreshController _refreshController = RefreshController(
    initialRefresh: false,
  );

  @override
  void initState() {
    super.initState();
    getAllData();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      context.read<HomeBloc>().add(GetAppVersionEvent());
    });
  }

  Future<void> getAllData() async {
    context.read<HomeBloc>()
      ..add(GetMatchCategoriesEvent())
      ..add(GetGameEvent())
      ..add(GetProductsEvent())
      ..add(GetNewsEvent());
    await Future.delayed(const Duration(seconds: 1));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HomeBloc, HomeState>(
      listenWhen: (previous, current) {
        return previous.appUpdateStatus != current.appUpdateStatus;
      },
      listener: (BuildContext context, HomeState state) {
        switch (state.appUpdateStatus) {
          case AppUpdateStatus.notWorking:
            showTechnicalWorkDialog(context);
            break;
          case AppUpdateStatus.optional:
            showUpdateDialog(context, required: false);
            break;
          case AppUpdateStatus.required:
            showUpdateDialog(context, required: true);
            break;
          default:
        }
      },
      builder: (context, state) {
        return Stack(
          children: [
            Container(
              height: 200,
              decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(bottom: Radius.circular(30)),
                  color: AppColors.primary),
            ),
            SmartRefresher(
              controller: _refreshController,
              header: BezierHeader(
                bezierColor: AppColors.primary2, // Primary color for pull area
                rectHeight: 60, // Height of the refresh area
                child: Center(
                  child: SvgPicture.asset(AppAssets.whiteLogo, height: 30),
                ),
              ),
              onRefresh: () async {
                await getAllData();
                _refreshController.refreshCompleted();
              },
              child: CustomScrollView(
                physics: const AlwaysScrollableScrollPhysics(
                  parent: ClampingScrollPhysics(),
                ),
                slivers: [
                  const WHomeAppBar(),
                  const WGames(),
                  const WProducts(),
                  SliverToBoxAdapter(child: Gap(context.padding.bottom)),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
