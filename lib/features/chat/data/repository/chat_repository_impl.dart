import 'package:dio/dio.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/chat/data/datasource/chat_datasource.dart';
import 'package:echipta/features/chat/domain/entities/chat_entity.dart';
import 'package:echipta/features/chat/domain/repository/chat_repository.dart';

class ChatRepositoryImpl implements ChatRepository {
  final ChatDatasource _datasource = serviceLocator<ChatDatasourceImpl>();
  @override
  Future<Either<Failure, List<ChatEntity>>> getChats() async {
    try {
      final result = await _datasource.getChats();
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey));
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, void>> sendMessage(ChatParams params) async {
    try {
      final result = await _datasource.sendMessage(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey));
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }
}
