import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/chat/data/models/chat_model.dart';
import 'package:echipta/features/common/models/error_model.dart';

abstract class ChatDatasource {
  Future<List<ChatModel>> getChats();
  Future<void> sendMessage(ChatParams params);
}

class ChatDatasourceImpl implements ChatDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  @override
  Future<List<ChatModel>> getChats() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/chat/get-chat/",
          options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"]["data"] as List)
            .map((e) => ChatModel.fromJson(e as Map<String, dynamic>))
            .toList();
      } else {
        if (response.data is Map<String, dynamic>) {
          final error = ErrorModel.fromJson(response.data);
          if (error.errors.isNotEmpty) {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: error.errors.first.message,
                errorKey: error.errors.first.error);
          } else {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: response.data.toString(),
                errorKey: error.errors.first.error);
          }
        } else {
          throw ServerException(
              statusCode: response.statusCode ?? 500,
              errorMessage: response.data.toString(),
              errorKey: "Unknown error");
        }
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<void> sendMessage(ChatParams params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {"message": params.message};
      final response = await _dio.post("/chat/new-message/",
          data: data, options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return;
      } else {
        if (response.data is Map<String, dynamic>) {
          final error = ErrorModel.fromJson(response.data);
          if (error.errors.isNotEmpty) {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: error.errors.first.message,
                errorKey: error.errors.first.error);
          } else {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: response.data.toString(),
                errorKey: error.errors.first.error);
          }
        } else {
          throw ServerException(
              statusCode: response.statusCode ?? 500,
              errorMessage: response.data.toString(),
              errorKey: "Unknown error");
        }
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
