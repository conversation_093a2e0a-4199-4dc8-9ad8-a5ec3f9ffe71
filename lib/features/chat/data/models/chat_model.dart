// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/chat/domain/entities/chat_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'chat_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class ChatModel extends ChatEntity {
  const ChatModel({
    super.client_id,
    super.created_at,
    super.file,
    super.id,
    super.message,
    super.type,
    super.updated_at,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) =>
      _$ChatModelFromJson(json);
}
