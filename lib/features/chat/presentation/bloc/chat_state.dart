// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'chat_bloc.dart';

class ChatState extends Equatable {
  final List<ChatEntity> chats;
  final FormzSubmissionStatus chatStutus;
  final FormzSubmissionStatus sendMessageStatus;
  const ChatState({
    this.chats = const [],
    this.chatStutus = FormzSubmissionStatus.initial,
    this.sendMessageStatus = FormzSubmissionStatus.initial,
  });

  @override
  List<Object> get props => [
        chats,
        chatStutus,
        sendMessageStatus,
      ];

  ChatState copyWith({
    List<ChatEntity>? chats,
    FormzSubmissionStatus? chatStutus,
    FormzSubmissionStatus? sendMessageStatus,
  }) {
    return ChatState(
      chats: chats ?? this.chats,
      chatStutus: chatStutus ?? this.chatStutus,
      sendMessageStatus: sendMessageStatus ?? this.sendMessageStatus,
    );
  }
}

final class ChatInitial extends ChatState {}
