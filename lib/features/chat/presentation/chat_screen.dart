// ignore_for_file: deprecated_member_use

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/chat/domain/entities/chat_entity.dart';
import 'package:echipta/features/chat/presentation/bloc/chat_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:easy_localization/easy_localization.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController controller = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(12))),
        title: Text(
          LocaleKeys.help.tr(),
          style: Theme.of(context)
              .textTheme
              .displaySmall!
              .copyWith(color: AppColors.white),
        ),
      ),
      body: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          if (state.chatStutus.isInProgress) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }
          return Column(
            children: [
              Expanded(
                  child: GroupedListView<ChatEntity, DateTime>(
                elements: state.chats.reversed.toList(),
                padding: const EdgeInsets.all(20),
                groupBy: (element) {
                  final date = DateTime.parse(element.created_at);
                  return DateTime(
                    date.year,
                    date.month,
                    date.day,
                  );
                },
                reverse: true,
                order: GroupedListOrder.DESC,
                groupHeaderBuilder: (element) => const SizedBox(),
                itemBuilder: (context, element) {
                  return BlocBuilder<ProfileBloc, ProfileState>(
                    builder: (context, state) {
                      final isSendedByMe = state.me.id == element.client_id;
                      return Align(
                        alignment: isSendedByMe
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        child: Container(
                          margin: const EdgeInsets.symmetric(vertical: 10),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                  topLeft: const Radius.circular(30),
                                  topRight: const Radius.circular(30),
                                  bottomLeft:
                                      Radius.circular(isSendedByMe ? 30 : 0),
                                  bottomRight:
                                      Radius.circular(isSendedByMe ? 0 : 30)),
                              color: isSendedByMe
                                  ? AppColors.checkBoxColor
                                  : AppColors.primary),
                          child: Text(
                            element.message,
                            style: Theme.of(context)
                                .textTheme
                                .headlineLarge!
                                .copyWith(
                                    color: isSendedByMe
                                        ? AppColors.primary
                                        : AppColors.white),
                          ),
                        ),
                      );
                    },
                  );
                },
              )),
              Padding(
                padding: const EdgeInsets.all(20),
                child: TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    suffixIcon: IconButton(
                        onPressed: () {
                          if (controller.text.isNotEmpty) {
                            context.read<ChatBloc>().add(
                                SendMessageEvent(message: controller.text));
                            controller.clear();
                            context.read<ChatBloc>().add(GetChatsEvent());
                          }
                        },
                        icon: const Icon(Icons.send)),
                  ),
                ),
              ),
              Gap(context.padding.bottom)
            ],
          );
        },
      ),
    );
  }
}
