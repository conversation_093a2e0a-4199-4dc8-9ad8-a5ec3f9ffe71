// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class ChatEntity extends Equatable {
  final int id;
  final int client_id;
  final String message;
  final String file;
  final int type;
  final String created_at;
  final String updated_at;

  const ChatEntity({
    this.id = 0,
    this.client_id = 0,
    this.message = "",
    this.file = "",
    this.type = 0,
    this.created_at = "",
    this.updated_at = "",
  });

  @override
  List<Object?> get props => [
        id,
        client_id,
        message,
        file,
        type,
        created_at,
        updated_at,
      ];

  ChatEntity copyWith({
    int? id,
    int? client_id,
    String? message,
    String? file,
    int? type,
    String? created_at,
    String? updated_at,
  }) {
    return ChatEntity(
      id: id ?? this.id,
      client_id: client_id ?? this.client_id,
      message: message ?? this.message,
      file: file ?? this.file,
      type: type ?? this.type,
      created_at: created_at ?? this.created_at,
      updated_at: updated_at ?? this.updated_at,
    );
  }
}
