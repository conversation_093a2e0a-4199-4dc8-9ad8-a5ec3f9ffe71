import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/chat/data/repository/chat_repository_impl.dart';
import 'package:echipta/features/chat/domain/entities/chat_entity.dart';
import 'package:echipta/features/chat/domain/repository/chat_repository.dart';

class ChatUsecase extends UseCase<List<ChatEntity>, NoParams> {
  final ChatRepository _repository = serviceLocator<ChatRepositoryImpl>();
  @override
  Future<Either<Failure, List<ChatEntity>>> call(NoParams params) async {
    return await _repository.getChats();
  }
}
