import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/profile/domain/entities/my_ticket_entity.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';

abstract class ProfileRepository {
  Future<Either<Failure, UserEntity>> getUserData();
  Future<Either<Failure, dynamic>> getUserIDCard();
  Future<Either<Failure, void>> requestIdCard(IdCardParams params);
  Future<Either<Failure, List<MyTicketEntity>>> getMyTickets();
  Future<Either<Failure, void>> deleteUser();
}
