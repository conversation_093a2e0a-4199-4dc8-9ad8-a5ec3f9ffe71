// ignore_for_file: public_member_api_docs, sort_constructors_first, non_constant_identifier_names
import 'package:equatable/equatable.dart';

class IdCardEntity extends Equatable {
  final String serial_number;
  final String front_image;
  final String back_image;

  const IdCardEntity({
    this.serial_number = "",
    this.front_image = "",
    this.back_image = "",
  });
  @override
  List<Object?> get props => [
        serial_number,
        front_image,
        back_image,
      ];

  IdCardEntity copyWith({
    String? serial_number,
    String? front_image,
    String? back_image,
  }) {
    return IdCardEntity(
      serial_number: serial_number ?? this.serial_number,
      front_image: front_image ?? this.front_image,
      back_image: back_image ?? this.back_image,
    );
  }
}
