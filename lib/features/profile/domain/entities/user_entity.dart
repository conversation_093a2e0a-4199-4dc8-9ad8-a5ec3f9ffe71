// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:equatable/equatable.dart';

class UserEntity extends Equatable {
  final int id;
  final bool is_filed;
  final String full_name;
  final String birth_date;
  final String picture;
  final int balance;
  @ItemConverter()
  final ItemEntity team;
  final String phone;
  const UserEntity({
    this.id = 0,
    this.is_filed = false,
    this.full_name = "",
    this.birth_date = "",
    this.picture = "",
    this.balance = 0,
    this.team = const ItemEntity(),
    this.phone = "",
  });

  @override
  List<Object?> get props => [
        id,
        is_filed,
        full_name,
        birth_date,
        picture,
        balance,
        team,
        phone,
      ];

  UserEntity copyWith({
    int? id,
    bool? is_filed,
    String? full_name,
    String? birth_date,
    String? picture,
    int? balance,
    ItemEntity? team,
    String? phone,
  }) {
    return UserEntity(
      id: id ?? this.id,
      is_filed: is_filed ?? this.is_filed,
      full_name: full_name ?? this.full_name,
      birth_date: birth_date ?? this.birth_date,
      picture: picture ?? this.picture,
      balance: balance ?? this.balance,
      team: team ?? this.team,
      phone: phone ?? this.phone,
    );
  }
}
