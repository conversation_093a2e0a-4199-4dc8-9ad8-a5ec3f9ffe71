import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/profile/data/repository/profile_repository_impl.dart';
import 'package:echipta/features/profile/domain/repository/profile_repository.dart';

class RequestIdCardUseCase extends UseCase<void, IdCardParams> {
  final ProfileRepository _repository = serviceLocator<ProfileRepositoryImpl>();

  @override
  Future<Either<Failure, void>> call(IdCardParams params) async {
    return await _repository.requestIdCard(params);
  }
}
