import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/profile/data/repository/profile_repository_impl.dart';
import 'package:echipta/features/profile/domain/entities/my_ticket_entity.dart';
import 'package:echipta/features/profile/domain/repository/profile_repository.dart';

class MyTicketsUseCase extends UseCase<List<MyTicketEntity>, NoParams> {
  final ProfileRepository _repository = serviceLocator<ProfileRepositoryImpl>();

  @override
  Future<Either<Failure, List<MyTicketEntity>>> call(NoParams params) async {
    return await _repository.getMyTickets();
  }
}
