// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'user_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class UserModel extends UserEntity {
  const UserModel({
    super.balance,
    super.birth_date,
    super.full_name,
    super.id,
    super.is_filed,
    super.picture,
    super.team,
    super.phone,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}
