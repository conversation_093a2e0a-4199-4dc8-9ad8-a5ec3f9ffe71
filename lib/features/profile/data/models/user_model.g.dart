// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
  balance: (json['balance'] as num?)?.toInt() ?? 0,
  birth_date: json['birth_date'] as String? ?? "",
  full_name: json['full_name'] as String? ?? "",
  id: (json['id'] as num?)?.toInt() ?? 0,
  is_filed: json['is_filed'] as bool? ?? false,
  picture: json['picture'] as String? ?? "",
  team:
      json['team'] == null
          ? const ItemEntity()
          : const ItemConverter().fromJson(
            json['team'] as Map<String, dynamic>,
          ),
  phone: json['phone'] as String? ?? "",
);

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'id': instance.id,
  'is_filed': instance.is_filed,
  'full_name': instance.full_name,
  'birth_date': instance.birth_date,
  'picture': instance.picture,
  'balance': instance.balance,
  'team': const ItemConverter().toJson(instance.team),
  'phone': instance.phone,
};
