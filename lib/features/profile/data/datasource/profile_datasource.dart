import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/profile/data/models/id_card_model.dart';
import 'package:echipta/features/profile/data/models/my_ticket_model.dart';
import 'package:echipta/features/profile/data/models/user_model.dart';

abstract class ProfileDatasource {
  Future<UserModel> getUserData();
  Future<dynamic> getUserIDCard();
  Future<void> requestIdCard(IdCardParams params);
  Future<List<MyTicketModel>> getMyTickets();
  Future<void> deleteUser();
}

class ProfileDatasourceImpl implements ProfileDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  @override
  Future<UserModel> getUserData() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/clients/me/",
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return UserModel.fromJson(response.data["data"]);
      } else {
        final message = (response.data as Map<String, dynamic>).values
            .toString()
            .replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<dynamic> getUserIDCard() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/clients/cards/my-id-card/",
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        if (response.data["data"] == null) {
          return null;
        } else if (response.data["stutus"] ==
            "payment_success_and_moderation") {
          return [];
        }
        return IdCardModel.fromJson(response.data["data"]);
      } else {
        final message = (response.data as Map<String, dynamic>).values
            .toString()
            .replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<void> requestIdCard(IdCardParams params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {
        "type": params.type,
        "sector": params.sector,
        "row": params.row,
        "seat": params.seat,
      };
      final response = await _dio.post(
        "/clients/cards/request-card/",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );

      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return;
      } else {
        final message = (response.data as Map<String, dynamic>).values
            .toString()
            .replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<List<MyTicketModel>> getMyTickets() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get(
        "/clients/my-tickets/",
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"]["items"] as List)
            .map((e) => MyTicketModel.fromJson(e as Map<String, dynamic>))
            .toList();
      } else {
        final message = (response.data as Map<String, dynamic>).values
            .toString()
            .replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<void> deleteUser() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.delete(
        "/clients/delete-account",
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return;
      } else {
        final message = (response.data as Map<String, dynamic>).values
            .toString()
            .replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
