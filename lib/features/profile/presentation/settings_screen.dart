import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/widgets/w_profile_item.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.black,
        title: Text(
          LocaleKeys.settings.tr(),
          style: context.textTheme.displaySmall!.copyWith(
            color: AppColors.black,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(20, 10, 20, 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            WProfileItem(
              title: LocaleKeys.appPin.tr(),
              icon: AppAssets.lock,
              route: AppRouter.setPincode,
            ),
            Gap(16),
            WProfileItem(
              title: LocaleKeys.appLang.tr(),
              icon: AppAssets.language,
              route: AppRouter.lang,
            ),
          ],
        ),
      ),
    );
  }
}
