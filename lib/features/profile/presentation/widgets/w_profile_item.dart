import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:gap/gap.dart';

class WProfileItem extends StatelessWidget {
  const WProfileItem({
    super.key,
    required this.title,
    required this.icon,
    required this.route,
    this.iconColor = AppColors.black,
    this.hasTrailing = true,
    this.txtColor = AppColors.black,
    this.onTap,
  });
  final String title;
  final String icon;
  final String route;
  final Color? iconColor;
  final bool? hasTrailing;
  final Color? txtColor;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          onTap ??
          () {
            if (route == "logout") {
              StorageRepository.deleteString(StoreKeys.pincode);
              context.read<AuthBloc>().add(const LogoutEvent());
              context.go(AppRouter.auth);
            }
            context.push(route);
          },
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 10, 10, 10),
        decoration: BoxDecoration(color: AppColors.fillColor, borderRadius: BorderRadius.circular(100)),
        child: Row(
          children: [
            SvgPicture.asset(icon, width: 24, color: iconColor),
            const Gap(8),
            Text(title, style: context.textTheme.bodyLarge!.copyWith(color: txtColor)),
            if (hasTrailing!) ...[const Spacer(), SvgPicture.asset(AppAssets.chevronRight)],
          ],
        ),
      ),
    );
  }
}
