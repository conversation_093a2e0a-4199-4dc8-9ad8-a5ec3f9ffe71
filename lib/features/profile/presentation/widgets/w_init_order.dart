import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/profile/presentation/widgets/w_order_idcard.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WInitOrder extends StatefulWidget {
  const WInitOrder({super.key});

  @override
  State<WInitOrder> createState() => _WInitOrderState();
}

class _WInitOrderState extends State<WInitOrder> {
  bool hasIDcard = false;
  bool orderedIdCard = false;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Builder(builder: (context) {
        if (orderedIdCard) {
          return const WOrderIdCard();
        }
        return Visibility(
          visible: !orderedIdCard || !hasIDcard,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(AppAssets.id),
                const Gap(20),
                Text(
                  "Sizda ID karta mavjud emas",
                  style: context.textTheme.displayLarge,
                ),
                const Gap(20),
                WButton(
                    onTap: () {
                      setState(() {
                        orderedIdCard = true;
                      });
                    },
                    txt: "Buyurtma qilish")
              ],
            ),
          ),
        );
      }),
    );
  }
}
