import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class WFlipCard extends StatefulWidget {
  const WFlipCard({super.key, required this.front, required this.back});
  final String front;
  final String back;
  @override
  State<WFlipCard> createState() => _WFlipCardState();
}

class _WFlipCardState extends State<WFlipCard>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> animation;
  bool isFront = true;
  bool isFronStart = true;
  double dragPosition = 0;

  @override
  void initState() {
    super.initState();

    controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    controller.addListener(() {
      setState(() {
        dragPosition = animation.value;
        setImageSide();
      });
    });
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final angle = dragPosition / 180 * pi;
    final transform = Matrix4.identity()
      ..setEntry(3, 2, 0.001)
      ..rotateX(angle);
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: GestureDetector(
        onVerticalDragStart: (details) {
          controller.stop();
          isFronStart = isFront;
        },
        onVerticalDragUpdate: (details) {
          setState(() {
            dragPosition += details.delta.dy;
            dragPosition %= 360;

            setImageSide();
          });
        },
        onVerticalDragEnd: (details) {
          final velocity = details.velocity.pixelsPerSecond.dy.abs();

          if (velocity >= 100) {
            isFront = !isFronStart;
          }

          double end = isFront ? (dragPosition > 180 ? 260 : 0) : 180;
          animation = Tween<double>(
            begin: dragPosition,
            end: end,
          ).animate(controller);

          controller.forward(from: 0);
        },
        child: Transform(
          transform: transform,
          alignment: Alignment.center,
          child: isFront
              ? CachedNetworkImage(imageUrl: widget.front)
              : Transform(
                  transform: Matrix4.identity()..rotateX(pi),
                  alignment: Alignment.center,
                  child: CachedNetworkImage(imageUrl: widget.back),
                ),
        ),
      ),
    );
  }

  void setImageSide() {
    if (dragPosition <= 90 || dragPosition >= 270) {
      isFront = true;
    } else {
      isFront = false;
    }
  }
}
