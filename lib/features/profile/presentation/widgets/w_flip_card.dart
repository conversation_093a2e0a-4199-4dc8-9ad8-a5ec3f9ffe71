import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:flutter/material.dart';

class WFlipCard extends StatefulWidget {
  const WFlipCard({super.key, required this.front, required this.back});
  final String front;
  final String back;
  @override
  State<WFlipCard> createState() => _WFlipCardState();
}

class _WFlipCardState extends State<WFlipCard>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> animation;
  bool isFront = true;
  bool isFronStart = true;
  double dragPosition = 0;

  @override
  void initState() {
    super.initState();

    controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    controller.addListener(() {
      setState(() {
        dragPosition = animation.value;
        setImageSide();
      });
    });

    // Start shimmer animation for placeholders
    _startShimmerAnimation();
  }

  void _startShimmerAnimation() {
    controller.repeat(
      period: const Duration(milliseconds: 1500),
    );
  }

  void _stopShimmerAnimation() {
    controller.stop();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final angle = dragPosition / 180 * pi;
    final transform = Matrix4.identity()
      ..setEntry(3, 2, 0.001)
      ..rotateX(angle);
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: GestureDetector(
        onVerticalDragStart: (details) {
          controller.stop();
          isFronStart = isFront;
        },
        onVerticalDragUpdate: (details) {
          setState(() {
            dragPosition += details.delta.dy;
            dragPosition %= 360;

            setImageSide();
          });
        },
        onVerticalDragEnd: (details) {
          final velocity = details.velocity.pixelsPerSecond.dy.abs();

          if (velocity >= 100) {
            isFront = !isFronStart;
          }

          double end = isFront ? (dragPosition > 180 ? 260 : 0) : 180;
          animation = Tween<double>(
            begin: dragPosition,
            end: end,
          ).animate(controller);

          controller.forward(from: 0);
        },
        child: Transform(
          transform: transform,
          alignment: Alignment.center,
          child: isFront
              ? CachedNetworkImage(
                  imageUrl: widget.front,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildPlaceholder(),
                  errorWidget: (context, url, error) => _buildErrorPlaceholder(),
                  imageBuilder: (context, imageProvider) {
                    _stopShimmerAnimation();
                    return Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        image: DecorationImage(
                          image: imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                )
              : Transform(
                  transform: Matrix4.identity()..rotateX(pi),
                  alignment: Alignment.center,
                  child: CachedNetworkImage(
                    imageUrl: widget.back,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildPlaceholder(),
                    errorWidget: (context, url, error) => _buildErrorPlaceholder(),
                    imageBuilder: (context, imageProvider) {
                      _stopShimmerAnimation();
                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          image: DecorationImage(
                            image: imageProvider,
                            fit: BoxFit.cover,
                          ),
                        ),
                      );
                    },
                  ),
                ),
        ),
      ),
    );
  }

  void setImageSide() {
    if (dragPosition <= 90 || dragPosition >= 270) {
      isFront = true;
    } else {
      isFront = false;
    }
  }

  Widget _buildPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.fillColor,
            AppColors.mediumGrey.withOpacity(0.3),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Animated shimmer effect
          AnimatedBuilder(
            animation: controller,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment(-1.0 + (controller.value * 3), -0.5),
                    end: Alignment(1.0 + (controller.value * 3), 0.5),
                    colors: [
                      Colors.transparent,
                      AppColors.white.withOpacity(0.3),
                      AppColors.white.withOpacity(0.5),
                      AppColors.white.withOpacity(0.3),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
                  ),
                ),
              );
            },
          ),
          // Card content placeholder
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top section - Logo area
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AnimatedBuilder(
                        animation: controller,
                        builder: (context, child) {
                          return Container(
                            width: 60,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.lightGrey.withOpacity(0.4 + (controller.value * 0.3)),
                              borderRadius: BorderRadius.circular(8),
                            ),
                          );
                        },
                      ),
                      AnimatedBuilder(
                        animation: controller,
                        builder: (context, child) {
                          return Container(
                            width: 80,
                            height: 30,
                            decoration: BoxDecoration(
                              color: AppColors.lightGrey.withOpacity(0.4 + (controller.value * 0.3)),
                              borderRadius: BorderRadius.circular(6),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  const Spacer(),
                  // Middle section - Card number
                  Container(
                    width: double.infinity,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppColors.lightGrey.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Bottom section - Name and expiry
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 120,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      Container(
                        width: 60,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppColors.fillColor,
        border: Border.all(
          color: AppColors.mediumGrey,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.credit_card_off,
            size: 48,
            color: AppColors.darkGrey,
          ),
          const SizedBox(height: 12),
          Text(
            'Karta yuklanmadi',
            style: TextStyle(
              color: AppColors.darkGrey,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Qaytadan urinib ko\'ring',
            style: TextStyle(
              color: AppColors.lightGrey,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
