// ignore_for_file: deprecated_member_use

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/profile/presentation/widgets/w_card_item.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';

class WBalanceCard extends StatelessWidget {
  const WBalanceCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Ink(
      decoration: BoxDecoration(color: AppColors.primary),
      child: Column(
        children: [
          Gap(20),
          <PERSON><PERSON><PERSON><PERSON>(
            height: 203,
            child: ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: 20),
              separatorBuilder: (context, index) => const Gap(10),
              itemCount: 3,
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return BlocBuilder<ProfileBloc, ProfileState>(
                    builder: (context, state) {
                      final user = state.me;
                      return Container(
                        height: 203,
                        width: MediaQuery.of(context).size.width * 0.8,
                        decoration: BoxDecoration(
                          color: Color(0xff493FD9),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SvgPicture.asset(
                              AppAssets.whiteLogo,
                              width: 40,
                              color: AppColors.white,
                            ),
                            const Spacer(),
                            Text(
                              LocaleKeys.profileBalance2.tr(),
                              style: context.textTheme.bodyLarge!.copyWith(
                                color: AppColors.white,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                final value = ClipboardData(
                                  text: user.id.toString(),
                                );
                                Clipboard.setData(value).then((value) {
                                  Fluttertoast.showToast(
                                    msg: LocaleKeys.idcardNumberCopied.tr(),
                                    fontSize: 18,
                                  );
                                });
                              },
                              child: Row(
                                children: [
                                  Text(
                                    user.id.toString(),
                                    style: context.textTheme.displayLarge!
                                        .copyWith(color: AppColors.white),
                                  ),
                                  const Gap(10),
                                  const Icon(
                                    Icons.copy,
                                    color: AppColors.white,
                                  ),
                                ],
                              ),
                            ),
                            const Gap(6),
                            Text(
                              LocaleKeys.price.tr(
                                namedArgs: {"price": user.balance.toString()},
                              ),
                              style: context.textTheme.displayLarge!.copyWith(
                                color: AppColors.white,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  );
                }
                return WCardItem();
              },
            ),
          ),
          const Gap(20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: WButton(
              onTap: () {},
              txt: LocaleKeys.addCard.tr(),
              btnColor: Color(0xff493FD9),
            ),
          ),
        ],
      ),
    );
  }
}
