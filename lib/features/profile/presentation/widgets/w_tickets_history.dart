import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/order/domain/entities/order_history_entity.dart';
import 'package:echipta/features/profile/presentation/widgets/w_ticket_order_item.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:grouped_list/grouped_list.dart';

class WTicketsHistory extends StatelessWidget {
  const WTicketsHistory({super.key, required this.tickets});

  final List<OrderHistoryEntity> tickets;

  @override
  Widget build(BuildContext context) {
    if(tickets.isEmpty){
      return WEmptyScreen();
    }
    return GroupedListView(
      padding: EdgeInsets.fromLTRB(20, 5, 20, 20 + context.padding.bottom),
      elements: tickets,
      groupBy:
          (element) => "",
      separator: const Gap(10),
      groupSeparatorBuilder: (value) {
        return Text(value, style: context.textTheme.headlineLarge);
      },
      itemBuilder: (context, element) {
        return WTicketOrderItem(item: element);
      },
    );
  }
}
