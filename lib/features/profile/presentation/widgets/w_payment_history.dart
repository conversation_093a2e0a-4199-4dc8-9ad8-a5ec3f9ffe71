import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WPaymentHistory extends StatelessWidget {
  const WPaymentHistory({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 0,
      left: 0,
      bottom: 0,
      child: Ink(
        padding: EdgeInsets.fromLTRB(20, 30, 20, 0),
        height: MediaQuery.of(context).size.height * 0.53,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(50)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(LocaleKeys.paymentHistory.tr(), style: context.textTheme.displaySmall),
            Gap(10),
            Expanded(
              child: ListView.separated(
                padding: EdgeInsets.zero,
                itemCount: 10,
                separatorBuilder: (context, index) => Gap(10),
                itemBuilder: (context, index) {
                  return Container(
                    decoration: BoxDecoration(
                      color: index.isEven ? AppColors.green3 : AppColors.red3,
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: ListTile(
                      title: Text(
                        "50 000 uzs",
                        style: context.textTheme.bodyLarge!.copyWith(
                          color: index.isEven ? AppColors.green : AppColors.red,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      subtitle: Text("Andijon - Nasaf uchrashuvi uchun chipta"),
                      trailing: Image.asset(
                        index.isEven ? AppAssets.success : AppAssets.error,
                        width: 40,
                      ),
                    ),
                  );
                },
              ),
            ),
            Gap(10),
            WButton(onTap: () {}, txt: LocaleKeys.topup.tr()),
            Gap(context.padding.bottom),
          ],
        ),
      ),
    );
  }
}
