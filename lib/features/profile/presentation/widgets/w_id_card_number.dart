import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';

class WIdCardNumber extends StatelessWidget {
  const WIdCardNumber({super.key, required this.idcardNumber});

  final String idcardNumber;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            LocaleKeys.idcardNumber.tr(),
            style: context.textTheme.displayMedium,
          ),
        ),
        const Gap(10),
        GestureDetector(
          onTap: () {
            final value = ClipboardData(text: idcardNumber);
            Clipboard.setData(value).then((value) {
              Fluttertoast.showToast(
                msg: LocaleKeys.idcardNumberCopied.tr(),
                fontSize: 18,
              );
            });
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(32),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  idcardNumber,
                  style: context.textTheme.bodyLarge!.copyWith(
                    color: AppColors.white,
                  ),
                ),
                const Gap(10),
                const Icon(Icons.copy, color: AppColors.white),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
