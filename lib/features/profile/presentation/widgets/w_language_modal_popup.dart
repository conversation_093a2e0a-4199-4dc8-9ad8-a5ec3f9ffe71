import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';

class WLangugageModalPopup extends StatelessWidget {
  const WLangugageModalPopup({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "Tilni tanlang",
            style: context.textTheme.labelLarge,
          ),
          const Gap(20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ListTile(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              tileColor: AppColors.fillColor,
              leading: SvgPicture.asset(AppAssets.uz),
              title: const Text("O'zbek tili"),
            ),
          ),
          Gap(16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ListTile(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16)),
              tileColor: AppColors.fillColor,
              leading: SvgPicture.asset(AppAssets.ru),
              title: const Text("Руский язык"),
            ),
          ),
          Gap(20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: WButton(onTap: () {}, txt: LocaleKeys.confirm.tr()),
          ),
          Gap(context.padding.bottom),
        ],
      ),
    );
  }
}
