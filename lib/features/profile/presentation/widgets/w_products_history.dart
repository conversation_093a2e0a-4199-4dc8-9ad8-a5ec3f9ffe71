import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/core/utils/size_config.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/order/domain/entities/order_history_entity.dart';
import 'package:echipta/features/order/presentation/widgets/w_bill_information_item.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
class WProductsHistory extends StatelessWidget {
  const WProductsHistory({super.key, required this.products});

  final List<OrderHistoryEntity> products;

  @override
  Widget build(BuildContext context) {
    if(products.isEmpty){
      return WEmptyScreen();
    }
    return ListView.builder(
      padding: EdgeInsets.fromLTRB(20, 20, 20, 20 + context.padding.bottom),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return ListView.separated(
          separatorBuilder: (context, index) => const Gap(10),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: product.items!.length,
          itemBuilder: (context, index) {
            final item = product.items![index];
            final prod = item.product;
            return Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.mediumGrey),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                          imageUrl: prod.image,
                          width: wi(100),
                          height: he(100),
                          fit: BoxFit.cover,
                          errorWidget:
                              (context, url, error) => const Placeholder(),
                        ),
                      ),
                      const Gap(10),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              context.locale.languageCode == "uz"
                                  ? prod.title_uz
                                  : prod.title_ru,
                              style: context.textTheme.bodyLarge,
                            ),
                            const Gap(4),
                            Text(
                              context.locale.languageCode == "uz"
                                  ? prod.description_uz
                                  : prod.description_ru,
                              style: context.textTheme.labelSmall!.copyWith(
                                color: AppColors.darkGrey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const Gap(20),
                  WBillInformationItem(
                    title: "Yetkazib berildi",
                    subtitle:
                        "${prod.price.toDouble().formatAsSpaceSeparated()} so'm",
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}