// ignore_for_file: deprecated_member_use

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class WCardItem extends StatelessWidget {
  const WCardItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 203,
      width: MediaQuery.of(context).size.width * 0.8,
      decoration: BoxDecoration(
        color: Color(0xff493FD9),
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SvgPicture.asset(
                AppAssets.sqb,
                width: 70,
                color: AppColors.white,
              ),
              const Spacer(),
              Image.asset(AppAssets.humo, width: 40),
            ],
          ),
          const Spacer(),
          Text(
            "SQB Humo",
            style: context.textTheme.bodyLarge!.copyWith(
              color: AppColors.white,
            ),
          ),
          Text(
            "9860 0000 **** 0000",
            style: context.textTheme.bodySmall!.copyWith(
              color: AppColors.white,
            ),
          ),
          const Gap(6),
          Text(
            LocaleKeys.price.tr(namedArgs: {
              "price": "100 000",
            }),
            style: context.textTheme.displayLarge!.copyWith(
              color: AppColors.white,
            ),
          ),
          const Gap(4),
          Text(
            "Samandar Maxamadjonov",
            style: context.textTheme.bodyLarge!.copyWith(
              color: AppColors.white,
            ),
          ),
        ],
      ),
    );
  }
}
