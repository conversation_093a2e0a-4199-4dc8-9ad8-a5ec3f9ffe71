import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class WOrderIdCard extends StatelessWidget {
  const WOrderIdCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Buyurtma qilish", style: context.textTheme.bodyLarge),
            const Gap(20),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: () {
                          context.read<ProfileBloc>().add(const SelectIdCardTypeEvent(type: IdCardTypeEnum.idcard));
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 100,
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: AppColors.primary),
                          child:
                              state.idCardType == IdCardTypeEnum.idcard
                                  ? Container(
                                    width: 40,
                                    height: 40,
                                    alignment: Alignment.center,
                                    decoration: const BoxDecoration(color: Color(0xff0A004A), shape: BoxShape.circle),
                                    child: const Icon(Icons.check, color: AppColors.white),
                                  )
                                  : SvgPicture.asset(AppAssets.idcard, color: AppColors.white, width: 70),
                        ),
                      ),
                      const Gap(8),
                      Text("Oddiy ID karta", style: context.textTheme.headlineLarge),
                    ],
                  ),
                ),
                const Gap(20),
                Expanded(
                  child: Column(
                    children: [
                      GestureDetector(
                        onTap: () {
                          context.read<ProfileBloc>().add(const SelectIdCardTypeEvent(type: IdCardTypeEnum.seasoncard));
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 100,
                          decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: AppColors.red),
                          child:
                              state.idCardType == IdCardTypeEnum.seasoncard
                                  ? Container(
                                    width: 40,
                                    height: 40,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: AppColors.black.withOpacity(0.5),
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(Icons.check, color: AppColors.white),
                                  )
                                  : SvgPicture.asset(AppAssets.stadium, color: AppColors.white, width: 70),
                        ),
                      ),
                      const Gap(8),
                      Text("Mavsumiy karta", style: context.textTheme.headlineLarge),
                    ],
                  ),
                ),
              ],
            ),
            // const WAddressDropdown(),
            const Spacer(),
            WButton(
              onTap: () {
                context.push(AppRouter.idsector);
              },
              txt: "Toifani o‘zgartirish",
            ),
            Gap(context.padding.bottom),
          ],
        );
      },
    );
  }
}
