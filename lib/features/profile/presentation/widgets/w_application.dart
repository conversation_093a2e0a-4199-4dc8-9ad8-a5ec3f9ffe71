
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class WApplicationChecking extends StatelessWidget {
  const WApplicationChecking({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            AppAssets.timer,
            height: MediaQuery.of(context).size.height * 0.4,
          ),
          Text(
            LocaleKeys.recieptIsChecking.tr(),
            style: context.textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }
}
