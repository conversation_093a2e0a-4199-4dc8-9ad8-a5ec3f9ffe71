// ignore_for_file: deprecated_member_use

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/widgets/w_balance_card.dart';
import 'package:echipta/features/profile/presentation/widgets/w_payment_history.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';

class MyCardsScreen extends StatelessWidget {
  const MyCardsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        surfaceTintColor: AppColors.primary,
        title: Text(
          LocaleKeys.mycards.tr(),
          style: context.textTheme.displaySmall!.copyWith(
            color: AppColors.white,
          ),
        ),
      ),
      body: St<PERSON>(children: [W<PERSON><PERSON><PERSON><PERSON><PERSON>(), WPaymentHistory()]),
    );
  }
}
