import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_text_field.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

class ProfileEditScreen extends StatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController birthDateController = TextEditingController();
  XFile? imageFile;

  @override
  void initState() {
    super.initState();
    final user = context.read<ProfileBloc>().state.me;
    fullNameController.text = user.full_name;
    birthDateController.text = user.birth_date;
    // imageFile = File.fromUri(Uri.parse(user.picture));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Profilni tahrirlash",
          style: context.textTheme.displaySmall,
        ),
      ),
      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          final user = state.me;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Column(
              children: [
                Center(
                  child: SizedBox(
                    width: 100,
                    height: 100,
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(200),
                          child: CachedNetworkImage(
                            imageUrl: user.picture,
                            width: 100,
                            fit: BoxFit.cover,
                            height: 100,
                            errorWidget: (context, url, error) {
                              return Container(
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: AppColors.darkGrey,
                                  borderRadius: BorderRadius.circular(100),
                                ),
                                child: SvgPicture.asset(
                                  AppAssets.person,
                                  width: 50,
                                  color: AppColors.white,
                                ),
                              );
                            },
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            final ImagePicker picker = ImagePicker();
                            final XFile? image = await picker.pickImage(
                              source: ImageSource.camera,
                              imageQuality: 50,
                              preferredCameraDevice: CameraDevice.front,
                            );

                            if (image == null) return;
                            setState(() {
                              imageFile = image;
                            });
                          },
                          child: Container(
                            width: 30,
                            height: 30,
                            alignment: Alignment.center,
                            decoration: const BoxDecoration(
                              color: AppColors.yellow,
                              shape: BoxShape.circle,
                            ),
                            child: SvgPicture.asset(AppAssets.edit),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Gap(20),
                WTextField(
                  onChanged: (value) {
                    context.read<AuthBloc>().add(EnterFullNameEvent(value));
                  },
                  controller: fullNameController,
                  isEmpty: fullNameController.text.isEmpty,
                  labelText: LocaleKeys.fio.tr(),
                  prefixIcon: Icons.person,
                ),
                const Gap(16),
                WTextField(
                  onChanged: (value) {
                    context.read<AuthBloc>().add(EnterBirthDateEvent(value));
                  },
                  controller: birthDateController,
                  isEmpty: birthDateController.text.isEmpty,
                  labelText: LocaleKeys.birthDate.tr(),
                  prefixIcon: Icons.date_range_outlined,
                ),
                Spacer(),
                WButton(
                  onTap: () {
                    context.read<AuthBloc>().add(
                      SubmitRegisterEvent(
                        file: imageFile ?? XFile(""),
                        onError: (p0) {},
                        onSuccess: () {
                          Navigator.pop(context);
                        },
                      ),
                    );
                  },
                  txt: "Saqlash",
                ),
                Gap(10),
                WButton(
                  onTap: () {
                    context.read<AuthBloc>().add(
                      DeleteUserEvent(
                        onError: (p0) {},
                        onSuccess: () {
                          context.go(AppRouter.auth);
                          context.read<AuthBloc>().add(LogoutEvent());
                        },
                      ),
                    );
                  },
                  txt: "Profilni o'chirish",
                  btnColor: AppColors.red,
                  txtColor: AppColors.white,
                ),
                Gap(context.padding.bottom),
              ],
            ),
          );
        },
      ),
    );
  }
}
