import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/assets/app_constants.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:easy_localization/easy_localization.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.black,
        title: Text(
          LocaleKeys.privacyPolicy.tr(),
          style: context.textTheme.displaySmall!.copyWith(color: AppColors.black),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          HtmlWidget(
            textStyle: context.textTheme.bodyLarge!.copyWith(height: 1.1),
            context.locale.languageCode == "ru" ? AppConstants.privacyRu : AppConstants.privacyUz,
          ),
        ],
      ),
    );
  }
}
