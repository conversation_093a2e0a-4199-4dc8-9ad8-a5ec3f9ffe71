import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_error.dart';
import 'package:echipta/features/profile/data/models/id_card_model.dart';
import 'package:echipta/features/profile/domain/entities/id_card_entity.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/profile/presentation/widgets/w_application.dart';
import 'package:echipta/features/profile/presentation/widgets/w_flip_card.dart';
import 'package:echipta/features/profile/presentation/widgets/w_id_card_number.dart';
import 'package:echipta/features/profile/presentation/widgets/w_init_order.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';

class IdCardScreen extends StatefulWidget {
  const IdCardScreen({super.key});

  @override
  State<IdCardScreen> createState() => _IdCardScreenState();
}

class _IdCardScreenState extends State<IdCardScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  @override
  void initState() {
    super.initState();
    context.read<ProfileBloc>().add(GetIdCardEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: Text(
          LocaleKeys.myid.tr(),
          style: context.textTheme.displayMedium,
        ),
      ),
      body: BlocBuilder<ProfileBloc, ProfileState>(
        builder: (context, state) {
          if (state.idcardStatus.isInProgress) {
            return const Center(child: CircularProgressIndicator.adaptive());
          } else if (state.idcardStatus.isFailure) {
            return WError();
          } else if (state.idcard == const IdCardEntity()) {
            return const WInitOrder();
          } else if (state.idcardStatus.isSuccess &&
              state.idcard == const IdCardModel()) {
            return WApplicationChecking();
          } else {
            final idcard = state.idcard;
            return Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  WFlipCard(front: idcard.front_image, back: idcard.back_image),
                  const Gap(20),
                  WIdCardNumber(idcardNumber: idcard.serial_number),
                ],
              ),
            );
          }
        },
      ),
    );
  }
}
