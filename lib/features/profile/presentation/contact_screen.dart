import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class ContactScreen extends StatelessWidget {
  const ContactScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.black,
        title: Text(
          "Aloqa",
          style: context.textTheme.displaySmall!.copyWith(
            color: AppColors.black,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: <PERSON>umn(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: CachedNetworkImage(
                imageUrl:
                    "https://static-maps.yandex.ru/1.x/?ll=72.3297407794942,40.8138298293067&z=16&size=600,450&l=map&pt=72.3297407794942,40.8138298293067,pm2rdl",
              ),
            ),
            const Gap(20),
            ListTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              tileColor: AppColors.fillColor,
              leading: const Icon(Icons.phone),
              title: const Text("+998555000104"),
            ),
            const Gap(16),
            ListTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              tileColor: AppColors.fillColor,
              leading: const Icon(Icons.email),
              title: const Text("<EMAIL>"),
            ),
            const Gap(16),
            ListTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              tileColor: AppColors.fillColor,
              leading: const Icon(Icons.location_on_outlined),
              title: const Text(
                "Andijon viloyati, Qo`shariq dahasi, Bobur arena",
              ),
            ),
            const Gap(16),
            ListTile(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              tileColor: const Color(0xff42B0FF),
              leading: SvgPicture.asset(AppAssets.telegram),
              title: const Text(
                "Telegram orqali aloqa",
                style: TextStyle(color: AppColors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
