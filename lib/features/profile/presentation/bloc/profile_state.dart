// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'profile_bloc.dart';

class ProfileState extends Equatable {
  final UserEntity me;
  final FormzSubmissionStatus meStatus;
  final IdCardEntity idcard;
  final FormzSubmissionStatus idcardStatus;
  final FormzSubmissionStatus requestIdcardstatus;
  final List<MyTicketEntity> myTickets;
  final FormzSubmissionStatus myTicketsStatus;
  final IdCardTypeEnum idCardType;
  const ProfileState({
    this.me = const UserEntity(),
    this.meStatus = FormzSubmissionStatus.initial,
    this.idcard = const IdCardEntity(),
    this.idcardStatus = FormzSubmissionStatus.initial,
    this.requestIdcardstatus = FormzSubmissionStatus.initial,
    this.myTickets = const [],
    this.myTicketsStatus = FormzSubmissionStatus.initial,
    this.idCardType = IdCardTypeEnum.idcard,
  });

  @override
  List<Object> get props => [
        me,
        meStatus,
        idcard,
        idcardStatus,
        requestIdcardstatus,
        myTickets,
        myTicketsStatus,
        idCardType,
      ];

  ProfileState copyWith({
    UserEntity? me,
    FormzSubmissionStatus? meStatus,
    IdCardEntity? idcard,
    FormzSubmissionStatus? idcardStatus,
    FormzSubmissionStatus? requestIdcardstatus,
    List<MyTicketEntity>? myTickets,
    FormzSubmissionStatus? myTicketsStatus,
    IdCardTypeEnum? idCardType,
  }) {
    return ProfileState(
      me: me ?? this.me,
      meStatus: meStatus ?? this.meStatus,
      idcard: idcard ?? this.idcard,
      idcardStatus: idcardStatus ?? this.idcardStatus,
      requestIdcardstatus: requestIdcardstatus ?? this.requestIdcardstatus,
      myTickets: myTickets ?? this.myTickets,
      myTicketsStatus: myTicketsStatus ?? this.myTicketsStatus,
      idCardType: idCardType ?? this.idCardType,
    );
  }
}

final class ProfileInitial extends ProfileState {}
