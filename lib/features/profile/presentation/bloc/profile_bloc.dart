import 'package:bloc/bloc.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/profile/domain/entities/id_card_entity.dart';
import 'package:echipta/features/profile/domain/entities/my_ticket_entity.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';
import 'package:echipta/features/profile/domain/usecases/id_card_use_case.dart';
import 'package:echipta/features/profile/domain/usecases/my_tickets_use_case.dart';
import 'package:echipta/features/profile/domain/usecases/request_id_card_use_case.dart';
import 'package:echipta/features/profile/domain/usecases/user_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

part 'profile_event.dart';
part 'profile_state.dart';

enum IdCardTypeEnum { idcard, seasoncard }

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  final UserUseCase _userUseCase = UserUseCase();
  final IdCardUseCase _idCardUseCase = IdCardUseCase();
  final MyTicketsUseCase _myTicketsUseCase = MyTicketsUseCase();
  final RequestIdCardUseCase _requestIdCardUseCase = RequestIdCardUseCase();
  ProfileBloc() : super(ProfileInitial()) {
    on<ProfileEvent>((event, emit) async {
      emit(state.copyWith(meStatus: FormzSubmissionStatus.inProgress));
      final result = await _userUseCase.call(NoParams());
      if (result.isRight) {
        emit(
          state.copyWith(
            me: result.right,
            meStatus: FormzSubmissionStatus.success,
          ),
        );
      } else {
        emit(state.copyWith(meStatus: FormzSubmissionStatus.failure));
      }
    });
    on<GetIdCardEvent>((event, emit) async {
      emit(state.copyWith(idcardStatus: FormzSubmissionStatus.inProgress));
      final result = await _idCardUseCase.call(NoParams());
      if (result.isRight) {
        print(result.right);
        emit(
          state.copyWith(
            idcard: result.right,
            idcardStatus: FormzSubmissionStatus.success,
          ),
        );
      } else {
        emit(state.copyWith(idcardStatus: FormzSubmissionStatus.failure));
      }
    });
    on<GetMyTicketsEvent>((event, emit) async {
      emit(state.copyWith(myTicketsStatus: FormzSubmissionStatus.inProgress));
      final result = await _myTicketsUseCase.call(NoParams());
      if (result.isRight) {
        emit(
          state.copyWith(
            myTickets: result.right,
            myTicketsStatus: FormzSubmissionStatus.success,
          ),
        );
      } else {
        emit(state.copyWith(myTicketsStatus: FormzSubmissionStatus.failure));
      }
    });
    on<RequestIdCardEvent>((event, emit) async {
      emit(
        state.copyWith(requestIdcardstatus: FormzSubmissionStatus.inProgress),
      );
      final result = await _requestIdCardUseCase.call(
        IdCardParams(
          type: event.type,
          sector: event.sector,
          row: event.row,
          seat: event.seat,
        ),
      );
      if (result.isRight) {
        emit(
          state.copyWith(requestIdcardstatus: FormzSubmissionStatus.success),
        );
      } else {
        emit(
          state.copyWith(requestIdcardstatus: FormzSubmissionStatus.failure),
        );
      }
    });
    on<SelectIdCardTypeEvent>((event, emit) {
      emit(state.copyWith(idCardType: event.type));
    });
  }
}
