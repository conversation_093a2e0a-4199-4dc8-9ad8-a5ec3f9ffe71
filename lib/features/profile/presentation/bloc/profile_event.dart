part of 'profile_bloc.dart';

sealed class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object> get props => [];
}

class GetMeEvent extends ProfileEvent {}

class GetIdCardEvent extends ProfileEvent {}

class GetMyTicketsEvent extends ProfileEvent {}

class RequestIdCardEvent extends ProfileEvent {
  final Function(String) onError;
  final Function(String) onSuccess;
  final String type;
  final String sector;
  final String row;
  final String seat;

  const RequestIdCardEvent({
    required this.onError,
    required this.onSuccess,
    required this.type,
    required this.sector,
    required this.row,
    required this.seat,
  });
}

class SelectIdCardTypeEvent extends ProfileEvent {
  final IdCardTypeEnum type;

  const SelectIdCardTypeEvent({required this.type});
}
