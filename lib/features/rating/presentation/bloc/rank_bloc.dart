import 'package:bloc/bloc.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/features/rating/domain/entities/rank_entity.dart';
import 'package:echipta/features/rating/domain/usecases/rank_use_cases.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

part 'rank_event.dart';
part 'rank_state.dart';

class RankBloc extends Bloc<RankEvent, RankState> {
  final RankUseCase _rankUseCases = RankUseCase();
  RankBloc() : super(RankInitial()) {
    on<GetRankListEvent>((event, emit) async {
      emit(state.copyWith(ranksStatus: FormzSubmissionStatus.inProgress));
      final result = await _rankUseCases.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(
            ranksStatus: FormzSubmissionStatus.success, ranks: result.right));
      } else {
        emit(state.copyWith(ranksStatus: FormzSubmissionStatus.failure));
      }
    });
  }
}
