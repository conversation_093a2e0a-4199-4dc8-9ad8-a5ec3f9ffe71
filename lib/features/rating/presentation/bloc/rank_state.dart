// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'rank_bloc.dart';

class RankState extends Equatable {
  final List<RankEntity> ranks;
  final FormzSubmissionStatus ranksStatus;
  const RankState({
    this.ranks = const [],
    this.ranksStatus = FormzSubmissionStatus.initial,
  });

  @override
  List<Object> get props => [
        ranks,
        ranksStatus,
      ];

  RankState copyWith({
    List<RankEntity>? ranks,
    FormzSubmissionStatus? ranksStatus,
  }) {
    return RankState(
      ranks: ranks ?? this.ranks,
      ranksStatus: ranksStatus ?? this.ranksStatus,
    );
  }
}

final class RankInitial extends RankState {}
