import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/rating/domain/entities/rank_entity.dart';
import 'package:echipta/features/rating/presentation/bloc/rank_bloc.dart';
import 'package:echipta/features/rating/presentation/widgets/w_rank_appbar.dart';
import 'package:echipta/features/rating/presentation/widgets/w_rank_list.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';

class RatingsScreen extends StatelessWidget {
  const RatingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, user) {
        return BlocBuilder<RankBloc, RankState>(
          builder: (context, state) {
            if (state.ranksStatus.isInProgress) {
              return const Center(child: CircularProgressIndicator.adaptive());
            } else if (state.ranksStatus.isFailure) {
              return const Center(child: Text("Xatolik yuz berdi!"));
            } else if (state.ranksStatus.isSuccess && state.ranks.isEmpty) {
              return WEmptyScreen();
            }
            final hasUserRank = state.ranks.contains(RankEntity(client_id: user.me.id));

            return CustomScrollView(
              slivers: [
                if (hasUserRank)
                  const WRankAppBar()
                else
                  SliverAppBar(
                    backgroundColor: AppColors.primary,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(bottom: Radius.circular(12)),
                    ),
                    title: Text(
                      LocaleKeys.rating.tr(),
                      style: context.textTheme.bodyLarge!.copyWith(color: AppColors.white),
                    ),
                  ),
                const WRankList(),
              ],
            );
          },
        );
      },
    );
  }
}
