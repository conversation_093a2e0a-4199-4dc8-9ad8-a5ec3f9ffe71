import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/rating/domain/entities/rank_entity.dart';
import 'package:echipta/features/rating/presentation/bloc/rank_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class WRankAppBar extends StatelessWidget {
  const WRankAppBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      backgroundColor: AppColors.primary,
      title: Text(
        LocaleKeys.fanRanking.tr(),
        style: context.textTheme.displaySmall!.copyWith(color: AppColors.white),
      ),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(12))),
      expandedHeight: 400,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          alignment: Alignment.center,
          children: [
            Center(
              child: Image.asset(
                AppAssets.shine,
                fit: BoxFit.cover,
                width: double.maxFinite,
              ),
            ),
            Positioned(
              top: 120,
              child: BlocBuilder<ProfileBloc, ProfileState>(
                builder: (context, user) {
                  return BlocBuilder<RankBloc, RankState>(
                    builder: (context, state) {
                      final item = state.ranks.firstWhere(
                          (element) => element.client_id == user.me.id,
                          orElse: () => const RankEntity());
                      final i = state.ranks.indexOf(item);

                      return Column(
                        children: [
                          Container(
                            width: 110,
                            height: 110,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border:
                                  Border.all(color: AppColors.white, width: 7),
                              image: DecorationImage(
                                image: CachedNetworkImageProvider(
                                  user.me.picture,
                                ),
                              ),
                            ),
                          ),
                          const Gap(40),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                children: [
                                  Text(
                                    "${i + 1}",
                                    style: context.textTheme.displayLarge!
                                        .copyWith(
                                      color: AppColors.white,
                                      fontSize: 32,
                                    ),
                                  ),
                                  const Gap(4),
                                  Text(
                                    "O‘rnim",
                                    style: context.textTheme.displayLarge!
                                        .copyWith(color: AppColors.white),
                                  )
                                ],
                              ),
                              const Gap(32),
                              Column(
                                children: [
                                  Text(
                                    item.score.toString(),
                                    style: context.textTheme.displayLarge!
                                        .copyWith(
                                      color: AppColors.white,
                                      fontSize: 32,
                                    ),
                                  ),
                                  const Gap(4),
                                  Text(
                                    "Ballarim",
                                    style: context.textTheme.displayLarge!
                                        .copyWith(color: AppColors.white),
                                  )
                                ],
                              ),
                              const Gap(32),
                              Column(
                                children: [
                                  Text(
                                    item.order_count.toString(),
                                    style: context.textTheme.displayLarge!
                                        .copyWith(
                                      color: AppColors.white,
                                      fontSize: 32,
                                    ),
                                  ),
                                  const Gap(4),
                                  Text(
                                    "O‘yinlar",
                                    style: context.textTheme.displayLarge!
                                        .copyWith(color: AppColors.white),
                                  )
                                ],
                              )
                            ],
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Text(
                      "O‘rni",
                      style: context.textTheme.bodyLarge!
                          .copyWith(color: AppColors.white),
                    ),
                    const Gap(40),
                    Text(
                      "Muxlislar",
                      style: context.textTheme.bodyLarge!
                          .copyWith(color: AppColors.white),
                    ),
                    const Spacer(),
                    Text(
                      "O‘yin",
                      style: context.textTheme.bodyLarge!
                          .copyWith(color: AppColors.white),
                    ),
                    const Gap(10),
                    Text(
                      "Ball",
                      style: context.textTheme.bodyLarge!
                          .copyWith(color: AppColors.white),
                    ),
                    const Gap(50),
                    SvgPicture.asset(AppAssets.plusMinus)
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
