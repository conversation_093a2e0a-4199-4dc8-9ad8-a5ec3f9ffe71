import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/features/rating/presentation/bloc/rank_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';

class WRankList extends StatelessWidget {
  const WRankList({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RankBloc, RankState>(
      builder: (context, state) {
        if (state.ranksStatus.isSuccess) {
          return SliverPadding(
            padding: EdgeInsets.only(bottom: context.padding.bottom),
            sliver: SliverList.builder(
              itemCount: state.ranks.length,
              itemBuilder: (context, index) {
                final item = state.ranks[index];
                return BlocBuilder<ProfileBloc, ProfileState>(
                  builder: (context, state) {
                    return Container(
                      height: 50,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      decoration: BoxDecoration(
                        gradient:
                            item.client_id == state.me.id
                                ? LinearGradient(colors: [AppColors.green2, AppColors.green2.withOpacity(0)])
                                : null,
                      ),
                      child: Row(
                        children: [
                          SizedBox(
                            width: 20,
                            child: Text(
                              "${index + 1}",
                              style: context.textTheme.displayMedium!.copyWith(color: AppColors.primary),
                            ),
                          ),
                          const VerticalDivider(),
                          const Gap(10),
                          Expanded(child: Text(item.full_name, style: context.textTheme.headlineMedium)),
                          SizedBox(
                            width: 20,
                            child: Text(
                              "${item.order_count}",
                              style: context.textTheme.displayMedium!.copyWith(color: AppColors.primary),
                            ),
                          ),
                          const Gap(5),
                          const VerticalDivider(),
                          const Gap(5),
                          SizedBox(
                            width: 20,
                            child: Text(
                              "${item.score}",
                              style: context.textTheme.displayMedium!.copyWith(color: AppColors.primary),
                            ),
                          ),
                          const Gap(5),
                          const VerticalDivider(),
                          const Gap(5),
                          Text("+3", style: context.textTheme.displayMedium!.copyWith(color: AppColors.green)),
                          const Gap(2),
                          SvgPicture.asset(AppAssets.plus),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          );
        } else if (state.ranksStatus.isInProgress) {
          return const SliverFillRemaining(child: Center(child: CircularProgressIndicator.adaptive()));
        } else {
          return const SliverToBoxAdapter();
        }
      },
    );
  }
}
