import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/rating/data/repository/rank_repository_impl.dart';
import 'package:echipta/features/rating/domain/entities/rank_entity.dart';
import 'package:echipta/features/rating/domain/repository/rank_repository.dart';

class RankUseCase extends UseCase<List<RankEntity>, NoParams> {
  final RankRepository _repository = serviceLocator<RankRepositoryImpl>();
  @override
  Future<Either<Failure, List<RankEntity>>> call(NoParams params) async {
    return await _repository.getRanks();
  }
}
