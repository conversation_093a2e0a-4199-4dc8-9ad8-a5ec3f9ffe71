// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:equatable/equatable.dart';

class RankEntity extends Equatable {
  final int order_count;
  final int client_id;
  final String full_name;
  final int score;

  const RankEntity({
    this.order_count = 0,
    this.client_id = 0,
    this.full_name = "",
    this.score = 0,
  });

  @override
  List<Object?> get props => [
        order_count,
        client_id,
        full_name,
        score,
      ];

  RankEntity copyWith({
    int? order_count,
    int? client_id,
    String? full_name,
    int? score,
  }) {
    return RankEntity(
      order_count: order_count ?? this.order_count,
      client_id: client_id ?? this.client_id,
      full_name: full_name ?? this.full_name,
      score: score ?? this.score,
    );
  }
}
