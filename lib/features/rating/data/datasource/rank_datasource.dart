import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/rating/data/models/rank_model.dart';

abstract class RankDatasource {
  Future<List<RankModel>> getRanks();
}

class RankDatasourceImpl implements RankDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  @override
  Future<List<RankModel>> getRanks() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/clients/rating",
          options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List)
            .map((e) => RankModel.fromJson(e as Map<String, dynamic>))
            .toList();
      } else {
        final message = (response.data as Map<String, dynamic>)
            .values
            .toString()
            .replaceAll(
              RegExp(r'[\[\]]'),
              '',
            );
        throw CustomException(
          message: message,
          code: '${response.statusCode}',
        );
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
