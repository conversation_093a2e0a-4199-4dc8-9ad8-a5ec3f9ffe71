import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/home/<USER>/entities/sector_entity.dart';
import 'package:echipta/features/home/<USER>/entities/ticket_entity.dart';
import 'package:echipta/features/order/domain/entities/order_history_entity.dart';

abstract class OrderRepository {
  Future<Either<Failure, Map<String, dynamic>>> ticketPayment(OrderParams params);
  Future<Either<Failure, SectorEntity>> getSectorData(SectorParams params);
  Future<Either<Failure, TicketEntity>> getTicketInfo(OrderParams params);
  Future<Either<Failure, List<OrderHistoryEntity>>> getOrderHistory();
  Future<Either<Failure, dynamic>> orderIdCard(OrderParams params);
  Future<Either<Failure, dynamic>> getOrderStatus(IdParam params);
}
