import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/order/data/repository/order_repository_impl.dart';
import 'package:echipta/features/order/domain/repository/order_repository.dart';

class TicketPaymentUseCase extends UseCase<Map<String, dynamic>, OrderParams> {
  final OrderRepository _repository = serviceLocator<OrderRepositoryImpl>();
  @override
  Future<Either<Failure, Map<String, dynamic>>> call(OrderParams params) async {
    return await _repository.ticketPayment(params);
  }
}
