import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/order/data/repository/order_repository_impl.dart';
import 'package:echipta/features/order/domain/repository/order_repository.dart';

class OrderIdUseCase extends UseCase<dynamic, OrderParams> {
  final OrderRepository _repostiory = serviceLocator<OrderRepositoryImpl>();

  @override
  Future<Either<Failure, dynamic>> call(params) async {
    return await _repostiory.orderIdCard(params);
  }
}
