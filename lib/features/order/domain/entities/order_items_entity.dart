// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/order/data/models/order_items_model.dart';
import 'package:equatable/equatable.dart';

import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:json_annotation/json_annotation.dart';

class OrderItemsEntity extends Equatable {
  final int item_id;
  @ProductConverter()
  final ProductEntity product;
  const OrderItemsEntity({
    this.item_id = 0,
    this.product = const ProductEntity(),
  });
  @override
  List<Object?> get props => [
        item_id,
        product,
      ];

  OrderItemsEntity copyWith({
    int? item_id,
    ProductEntity? product,
  }) {
    return OrderItemsEntity(
      item_id: item_id ?? this.item_id,
      product: product ?? this.product,
    );
  }
}

class OrderItemsConverter
    extends JsonConverter<OrderItemsEntity, Map<String, dynamic>> {
  @override
  OrderItemsEntity fromJson(Map<String, dynamic> json) =>
      OrderItemsModel.fromJson(json);

  @override
  Map<String, dynamic> toJson(OrderItemsEntity object) => {};

  const OrderItemsConverter();
}
