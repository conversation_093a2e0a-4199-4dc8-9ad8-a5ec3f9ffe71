// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

import 'package:echipta/features/order/domain/entities/order_items_entity.dart';
import 'package:echipta/features/profile/domain/entities/my_ticket_entity.dart';

class OrderHistoryEntity extends Equatable {
  final int id;
  final int type;
  final int amount;
  @MyTicketConverter()
  final MyTicketEntity? ticket;
  @OrderItemsConverter()
  final List<OrderItemsEntity>? items;

  const OrderHistoryEntity({
    this.id = 0,
    this.type = -1,
    this.amount = 0,
    this.ticket,
    this.items,
  });
  @override
  List<Object?> get props => [
        id,
        type,
        amount,
        ticket,
        items,
      ];

  OrderHistoryEntity copyWith({
    int? id,
    int? type,
    int? amount,
    MyTicketEntity? ticket,
    List<OrderItemsEntity>? items,
  }) {
    return OrderHistoryEntity(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      ticket: ticket ?? this.ticket,
      items: items ?? this.items,
    );
  }
}
