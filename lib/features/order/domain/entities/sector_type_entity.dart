import 'package:flutter/material.dart';

final List<SectorTypeEntity> sectorTypes = [
  SectorTypeEntity(
    title: "Birinchi",
    color: const Color(0xff00A89C),
    titleRu: 'Первый',
  ),
  SectorTypeEntity(
    title: "<PERSON><PERSON><PERSON><PERSON>",
    color: const Color(0xff84C341),
    titleRu: 'Второй',
  ),
  SectorTypeEntity(
    title: "<PERSON><PERSON><PERSON>",
    color: const Color(0xff0E51DA),
    titleRu: 'Третий',
  ),
  SectorTypeEntity(
    title: "VIP",
    color: const Color(0xffF7991C),
    titleRu: 'VIP',
  ),
  SectorTypeEntity(
    title: "FAN",
    color: const Color(0xff8E40A9),
    titleRu: 'FAN',
  ),
  SectorTypeEntity(
    title: "Mehm<PERSON>",
    color: const Color(0xffE11837),
    titleRu: 'Гость',
  ),
];

class SectorTypeEntity {
  final String title;
  final String titleRu;
  final Color color;

  SectorTypeEntity({
    required this.title,
    required this.color,
    required this.titleRu,
  });
}
