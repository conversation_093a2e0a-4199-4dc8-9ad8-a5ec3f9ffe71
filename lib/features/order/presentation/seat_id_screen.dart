import 'dart:convert';

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_seat_id_modal_popup.dart';
import 'package:echipta/features/order/presentation/widgets/w_seat_types.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SeatIdScreen extends StatefulWidget {
  const SeatIdScreen({super.key, required this.sector});
  final String sector;

  @override
  State<SeatIdScreen> createState() => _SeatIdScreenState();
}

class _SeatIdScreenState extends State<SeatIdScreen> {
  late WebViewController _webViewController;

  @override
  void initState() {
    final match = context.read<HomeBloc>().state.game.currentMatch?.id ?? 0;

    _webViewController =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0xffffffff))
          ..enableZoom(true)
          ..addJavaScriptChannel(
            "eventSeat",
            onMessageReceived: (JavaScriptMessage message) {
              // Handle events received from JavaScript
              String event = message.message;

              final data = json.decode(event);
              context.read<OrderBloc>().add(
                GetTicketInfoEvent(match: match, sector: widget.sector, row: data["row"], seat: data["seat"]),
              );
              showModalBottomSheet(
                context: context,
                showDragHandle: true,
                elevation: 0,
                backgroundColor: AppColors.white,
                builder: (context) {
                  return WSeatIdModalPopup(data: data, sector: widget.sector);
                },
              );
            },
          )
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                // Update loading bar.
              },
              onPageStarted: (String url) {},
              onPageFinished: (String url) {},
              onHttpError: (HttpResponseError error) {},
              onWebResourceError: (WebResourceError error) {},
              onNavigationRequest: (NavigationRequest request) {
                if (request.url.startsWith('https://www.youtube.com/')) {
                  return NavigationDecision.prevent;
                }
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(Uri.parse('https://select-ticket.echipta.uz/select-ticket?sector=${widget.sector}'));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Joy tanlash", style: context.textTheme.displayMedium)),
      body: Padding(
        padding: EdgeInsets.fromLTRB(20, 10, 20, 20 + context.padding.bottom),
        child: Column(
          children: [
            Text(
              "2025 yilgi mavsum uchun joyingizni tanlang",
              textAlign: TextAlign.center,
              style: context.textTheme.bodyLarge,
            ),
            const Gap(20),
            Expanded(
              child: InteractiveViewer(
                boundaryMargin: const EdgeInsets.all(20),
                minScale: 1.0,
                maxScale: 5.0,
                child: WebViewWidget(controller: _webViewController),
              ),
            ),
            const Gap(20),
            const WSeatTypes(),
          ],
        ),
      ),
    );
  }
}
