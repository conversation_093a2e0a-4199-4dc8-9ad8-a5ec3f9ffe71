import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_match_item.dart';
import 'package:echipta/features/order/presentation/widgets/w_seat_modal_popup.dart';
import 'package:echipta/features/order/presentation/widgets/w_seat_types.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SelectSeatScreen extends StatefulWidget {
  const SelectSeatScreen({super.key, required this.sector});
  final String sector;

  @override
  State<SelectSeatScreen> createState() => _SelectSeatScreenState();
}

class _SelectSeatScreenState extends State<SelectSeatScreen> {
  late WebViewController _webViewController;

  @override
  void initState() {
    final match = context.read<OrderBloc>().state.selectedMatch;

    _webViewController =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0xffffffff))
          ..enableZoom(true)
          ..addJavaScriptChannel(
            "eventSeat",
            onMessageReceived: (JavaScriptMessage message) {
              // Handle events received from JavaScript
              String event = message.message;

              final data = json.decode(event);

              context.read<OrderBloc>().add(
                GetTicketInfoEvent(
                  match: match.id,
                  sector: widget.sector,
                  row: data["row"],
                  seat: data["seat"],
                ),
              );
              showModalBottomSheet(
                context: context,
                showDragHandle: true,
                elevation: 0,
                backgroundColor: AppColors.white,
                builder: (context) {
                  return WSeatModalPopup(data: data, sector: widget.sector);
                },
              );
            },
          )
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                // Update loading bar.
              },
              onPageStarted: (String url) {},
              onPageFinished: (String url) {},
              onHttpError: (HttpResponseError error) {},
              onWebResourceError: (WebResourceError error) {},
              onNavigationRequest: (NavigationRequest request) {
                if (request.url.startsWith('https://www.youtube.com/')) {
                  return NavigationDecision.prevent;
                }
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(
            Uri.parse(
              'https://select-ticket.echipta.uz/select-ticket?match=${match.id}&sector=${widget.sector}',
            ),
          );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(LocaleKeys.selectSeat.tr(), style: context.textTheme.displayMedium),
      ),
      body: ListView(
        padding: const EdgeInsets.all(20.0),
        children: [
          BlocBuilder<OrderBloc, OrderState>(
            builder: (context, state) {
              return WMatchItem(item: state.selectedMatch, readOnly: true);
            },
          ),
          const Gap(20),
          SizedBox(
            height: 405,
            child: InteractiveViewer(
              boundaryMargin: const EdgeInsets.all(20),
              minScale: 1.0,
              maxScale: 5.0,
              child: WebViewWidget(controller: _webViewController),
            ),
          ),
          const Gap(20),
          const WSeatTypes(),
          Gap(context.padding.bottom),
        ],
      ),
    );
  }
}
