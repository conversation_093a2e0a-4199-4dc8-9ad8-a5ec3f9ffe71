import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/order/domain/entities/sector_type_entity.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_match_item.dart';
import 'package:echipta/features/order/presentation/widgets/w_sector_modal_popup.dart';
import 'package:echipta/features/home/<USER>/widgets/w_sector_type_item.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SelectSectorScreen extends StatefulWidget {
  const SelectSectorScreen({super.key, required this.match});

  final MatchEntity match;

  @override
  State<SelectSectorScreen> createState() => _SelectSectorScreenState();
}

class _SelectSectorScreenState extends State<SelectSectorScreen> {
  late WebViewController _webViewController;
  var isLoading = false;

  @override
  void initState() {
    _webViewController =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0xffffffff))
          ..addJavaScriptChannel(
            "eventSector",
            onMessageReceived: (JavaScriptMessage message) {
              // Handle events received from JavaScript
              String event = message.message;
              log(event);
              context.read<OrderBloc>().add(
                GetSectorDataEvent(matchId: widget.match.id, sector: event),
              );

              showModalBottomSheet(
                context: context,
                showDragHandle: true,
                elevation: 0,
                backgroundColor: AppColors.white,
                builder: (context) {
                  return WSectorModalPopup(message: event);
                },
              );
            },
          )
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                // Update loading bar.
                log(progress.toString());
                if (progress == 100) {
                  log("Page loaded");
                }
                isLoading = progress != 100;
                setState(() {});
              },
              onPageStarted: (String url) {},
              onPageFinished: (String url) {},
              onHttpError: (HttpResponseError error) {
                log(error.toString());
              },
              onWebResourceError: (WebResourceError error) {
                log(error.toString());
              },
              onNavigationRequest: (NavigationRequest request) {
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(Uri.parse('https://select-ticket.echipta.uz/'));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          LocaleKeys.selectSector.tr(),
          style: context.textTheme.displayMedium,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.fromLTRB(20, 20, 20, 20 + context.padding.bottom),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            WMatchItem(item: widget.match, readOnly: true),
            const Gap(20),
            Expanded(
              child:
                  isLoading
                      ? const Center(
                        child: CircularProgressIndicator.adaptive(),
                      )
                      : WebViewWidget(controller: _webViewController),
            ),
            SizedBox(
              width: double.maxFinite,
              child: Wrap(
                spacing: 10,
                crossAxisAlignment: WrapCrossAlignment.center,
                runAlignment: WrapAlignment.center,
                alignment: WrapAlignment.center,
                runSpacing: 10,
                children: List.generate(sectorTypes.length, (index) {
                  final item = sectorTypes[index];
                  return WSectorTypeItem(type: item);
                }),
              ),
            ),
            // Gap(context.padding.bottom)
          ],
        ),
      ),
    );
  }
}
