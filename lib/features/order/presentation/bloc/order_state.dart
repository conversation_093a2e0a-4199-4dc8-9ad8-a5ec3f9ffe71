// ignore_for_file: public_member_api_docs, sort_constructors_first, constant_identifier_names
part of 'order_bloc.dart';

enum OrderPaymentType { alif, balance, card }

enum OrderType { ticket, gift, id, product }

enum ProductDeliveryType { delivery, in_store }

class OrderState extends Equatable {
  final OrderPaymentType paymentType;
  final FormzSubmissionStatus orderTicketStatus;
  final SectorEntity sector;
  final FormzSubmissionStatus sectorStatus;
  final TicketEntity ticket;
  final FormzSubmissionStatus ticketStatus;
  final MatchEntity selectedMatch;
  final OrderType orderType;
  final List<OrderHistoryEntity> orders;
  final FormzSubmissionStatus ordersStatus;
  final String selectedSizeOfProduct;
  final ProductDeliveryType productDeliveryType;
  final FormzSubmissionStatus orderIdStatus;
  final int orderInfo;
  final FormzSubmissionStatus orderStatus;

  const OrderState({
    this.paymentType = OrderPaymentType.balance,
    this.orderTicketStatus = FormzSubmissionStatus.initial,
    this.sector = const SectorEntity(),
    this.ticket = const TicketEntity(),
    this.sectorStatus = FormzSubmissionStatus.initial,
    this.ticketStatus = FormzSubmissionStatus.initial,
    this.selectedMatch = const MatchEntity(),
    this.orderType = OrderType.ticket,
    this.orders = const [],
    this.ordersStatus = FormzSubmissionStatus.initial,
    this.selectedSizeOfProduct = '',
    this.productDeliveryType = ProductDeliveryType.delivery,
    this.orderIdStatus = FormzSubmissionStatus.initial,
    this.orderStatus = FormzSubmissionStatus.initial,
    this.orderInfo = 0,
  });

  @override
  List<Object> get props => [
    paymentType,
    orderTicketStatus,
    sector,
    ticket,
    sectorStatus,
    ticketStatus,
    selectedMatch,
    orderType,
    orders,
    ordersStatus,
    selectedSizeOfProduct,
    productDeliveryType,
    orderIdStatus,
    orderStatus,
    orderInfo,
  ];

  OrderState copyWith({
    OrderPaymentType? paymentType,
    FormzSubmissionStatus? orderTicketStatus,
    SectorEntity? sector,
    FormzSubmissionStatus? sectorStatus,
    TicketEntity? ticket,
    FormzSubmissionStatus? ticketStatus,
    MatchEntity? selectedMatch,
    OrderType? orderType,
    List<OrderHistoryEntity>? orders,
    FormzSubmissionStatus? ordersStatus,
    String? selectedSizeOfProduct,
    ProductDeliveryType? productDeliveryType,
    FormzSubmissionStatus? orderIdStatus,
    int? orderInfo,
    FormzSubmissionStatus? orderStatus,
  }) {
    return OrderState(
      paymentType: paymentType ?? this.paymentType,
      orderTicketStatus: orderTicketStatus ?? this.orderTicketStatus,
      sector: sector ?? this.sector,
      sectorStatus: sectorStatus ?? this.sectorStatus,
      ticket: ticket ?? this.ticket,
      ticketStatus: ticketStatus ?? this.ticketStatus,
      selectedMatch: selectedMatch ?? this.selectedMatch,
      orderType: orderType ?? this.orderType,
      orders: orders ?? this.orders,
      ordersStatus: ordersStatus ?? this.ordersStatus,
      selectedSizeOfProduct:
          selectedSizeOfProduct ?? this.selectedSizeOfProduct,
      productDeliveryType: productDeliveryType ?? this.productDeliveryType,
      orderIdStatus: orderIdStatus ?? this.orderIdStatus,
      orderInfo: orderInfo ?? this.orderInfo,
      orderStatus: orderStatus ?? this.orderStatus,
    );
  }
}

final class OrderInitial extends OrderState {}
