// ignore_for_file: non_constant_identifier_names

part of 'order_bloc.dart';

sealed class OrderEvent extends Equatable {
  const OrderEvent();

  @override
  List<Object> get props => [];
}

class SelectOrderPaymentTypeEvent extends OrderEvent {
  final OrderPaymentType paymentType;

  const SelectOrderPaymentTypeEvent({required this.paymentType});
}

class OrderTicketEvent extends OrderEvent {
  final String paymentType;
  final String? userForGift;
  final Function(Map<String, dynamic>) onSuccess;
  final Function(String) onError;

  const OrderTicketEvent({
    required this.paymentType,
    required this.onSuccess,
    required this.onError,
    required this.userForGift,
  });
}

class GetSectorDataEvent extends OrderEvent {
  final String sector;
  final int matchId;
  const GetSectorDataEvent({required this.sector, required this.matchId});
}

class GetTicketInfoEvent extends OrderEvent {
  final int match;
  final String sector;
  final String row;
  final String seat;

  const GetTicketInfoEvent({
    required this.match,
    required this.sector,
    required this.row,
    required this.seat,
  });
}

class SelectMatchEvent extends OrderEvent {
  final MatchEntity match;
  const SelectMatchEvent({required this.match});
}

class SelectOrderTypeEvent extends OrderEvent {
  final OrderType orderType;
  const SelectOrderTypeEvent({required this.orderType});
}

class GetOrdersEvent extends OrderEvent {}

class SelectSizeOfProductEvent extends OrderEvent {
  final String size;
  const SelectSizeOfProductEvent(this.size);
}

class SelectProductDeliveryTypeEvent extends OrderEvent {
  final ProductDeliveryType deliveryType;
  const SelectProductDeliveryTypeEvent(this.deliveryType);
}

class OrderIdEvent extends OrderEvent {
  final Function(dynamic) onSuccess;
  final Function(String) onError;
  final int match_id;
  final String sector;
  final String row;
  final String seat;
  final String paymentType;
  final String type;
  final int city;

  const OrderIdEvent({
    required this.onSuccess,
    required this.onError,
    required this.match_id,
    required this.sector,
    required this.row,
    required this.seat,
    required this.paymentType,
    required this.type,
    required this.city,
  });
}

class GetOrderStatusEvent extends OrderEvent {
  final int orderId;
  const GetOrderStatusEvent({required this.orderId});
}
