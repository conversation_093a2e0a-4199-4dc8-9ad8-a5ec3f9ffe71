import 'package:bloc/bloc.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/home/<USER>/entities/sector_entity.dart';
import 'package:echipta/features/home/<USER>/entities/ticket_entity.dart';
import 'package:echipta/features/order/domain/entities/order_history_entity.dart';
import 'package:echipta/features/order/domain/usecases/order_history_use_case.dart';
import 'package:echipta/features/order/domain/usecases/order_id_use_case.dart';
import 'package:echipta/features/order/domain/usecases/order_status_use_case.dart';
import 'package:echipta/features/order/domain/usecases/sector_use_case.dart';
import 'package:echipta/features/order/domain/usecases/ticket_info_use_case.dart';
import 'package:echipta/features/order/domain/usecases/ticket_payment_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:formz/formz.dart';

part 'order_event.dart';
part 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  final TicketPaymentUseCase _ticketPaymentUseCase = TicketPaymentUseCase();

  final SectorUseCase _sectorUseCase = SectorUseCase();
  final TicketInfoUseCase _ticketInfoUseCase = TicketInfoUseCase();
  final OrderHistoryUseCase _orderHistoryUseCase = OrderHistoryUseCase();
  final OrderIdUseCase _orderIdUseCase = OrderIdUseCase();
  final OrderStatusUseCase _orderStatusUseCase = OrderStatusUseCase();
  OrderBloc() : super(OrderInitial()) {
    on<SelectOrderPaymentTypeEvent>((event, emit) {
      emit(state.copyWith(paymentType: event.paymentType));
    });
    on<OrderTicketEvent>((event, emit) async {
      emit(state.copyWith(orderTicketStatus: FormzSubmissionStatus.inProgress));
      final params = OrderParams(
        matchId: state.selectedMatch.id,
        sector: state.ticket.sector,
        row: state.ticket.row,
        seat: state.ticket.seat,
        userForGift: event.userForGift,
        paymentType: event.paymentType,
      );
      final result = await _ticketPaymentUseCase(params);
      if (result.isRight) {
        emit(state.copyWith(orderTicketStatus: FormzSubmissionStatus.success));
        event.onSuccess(result.right);
      } else {
        emit(state.copyWith(orderTicketStatus: FormzSubmissionStatus.failure));
        event.onError(result.left.errorMessage ?? "Error");
      }
    });
    on<GetSectorDataEvent>((event, emit) async {
      emit(state.copyWith(sectorStatus: FormzSubmissionStatus.inProgress));
      final params = SectorParams(sector: event.sector, match_id: event.matchId);
      final result = await _sectorUseCase.call(params);
      if (result.isRight) {
        emit(state.copyWith(sectorStatus: FormzSubmissionStatus.success, sector: result.right));
      } else {
        emit(state.copyWith(sectorStatus: FormzSubmissionStatus.failure));
      }
    });
    on<GetTicketInfoEvent>((event, emit) async {
      emit(state.copyWith(ticketStatus: FormzSubmissionStatus.inProgress));
      final params = OrderParams(
        matchId: event.match,
        sector: event.sector,
        row: event.row,
        userForGift: null,
        seat: event.seat,
      );
      final result = await _ticketInfoUseCase.call(params);
      if (result.isRight) {
        emit(state.copyWith(ticket: result.right, ticketStatus: FormzSubmissionStatus.success));
      } else {
        emit(state.copyWith(ticketStatus: FormzSubmissionStatus.failure));
      }
    });
    on<SelectMatchEvent>((event, emit) => emit(state.copyWith(selectedMatch: event.match)));
    on<SelectOrderTypeEvent>((event, emit) {
      if(kDebugMode){
        print("Order Type: ${event.orderType}");
      }
      emit(state.copyWith(orderType: event.orderType));
    });
    on<GetOrdersEvent>((event, emit) async {
      emit(state.copyWith(ordersStatus: FormzSubmissionStatus.inProgress));
      final result = await _orderHistoryUseCase.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(ordersStatus: FormzSubmissionStatus.success, orders: result.right));
      } else {
        emit(state.copyWith(ordersStatus: FormzSubmissionStatus.failure));
      }
    });
    on<SelectSizeOfProductEvent>((event, emit) => emit(state.copyWith(selectedSizeOfProduct: event.size)));
    on<SelectProductDeliveryTypeEvent>((event, emit) => emit(state.copyWith(productDeliveryType: event.deliveryType)));
    on<OrderIdEvent>((event, emit) async {
      emit(state.copyWith(orderIdStatus: FormzSubmissionStatus.inProgress));
      final params = OrderParams(
        matchId: event.match_id,
        sector: event.sector,
        row: event.row,
        seat: event.seat,
        userForGift: null,
        paymentType: event.paymentType,
        city: event.city,
        type: event.type,
      );
      final result = await _orderIdUseCase.call(params);
      if (result.isRight) {
        emit(state.copyWith(orderIdStatus: FormzSubmissionStatus.success));
        event.onSuccess(result.right);
      } else {
        emit(state.copyWith(orderIdStatus: FormzSubmissionStatus.failure));
        event.onError(result.left.errorMessage ?? "Error");
      }
    });
    on<GetOrderStatusEvent>((event, emit) async {
      emit(state.copyWith(orderStatus: FormzSubmissionStatus.inProgress));
      final params = IdParam(id: event.orderId);
      final result = await _orderStatusUseCase.call(params);
      if (result.isRight) {
        emit(state.copyWith(orderStatus: FormzSubmissionStatus.success, orderInfo: result.right));
      } else {
        emit(state.copyWith(orderStatus: FormzSubmissionStatus.failure, orderInfo: 0));
      }
    });
  }
}
