import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/order/presentation/widgets/w_panorama_body.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';

class PonaramaScreen extends StatelessWidget {
  const PonaramaScreen({
    super.key,
    this.isId = false,
  });
  final bool? isId;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        surfaceTintColor: AppColors.primary,
        title: Text(
          LocaleKeys.viziualView.tr(),
          style: Theme.of(context)
              .textTheme
              .labelLarge!
              .copyWith(color: AppColors.white),
        ),
      ),
      body: WPanoramaBody(
        isId: isId,
      ),
    );
  }
}
