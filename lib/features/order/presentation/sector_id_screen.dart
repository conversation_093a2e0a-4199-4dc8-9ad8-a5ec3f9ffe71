import 'dart:developer';

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/bloc/home_bloc.dart';
import 'package:echipta/features/order/domain/entities/sector_type_entity.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/home/<USER>/widgets/w_sector_type_item.dart';
import 'package:echipta/features/order/presentation/widgets/w_sector_modal_popup.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SectorIdScreen extends StatefulWidget {
  const SectorIdScreen({super.key});
  @override
  State<SectorIdScreen> createState() => _SectorIdScreenState();
}

class _SectorIdScreenState extends State<SectorIdScreen> {
  late WebViewController _webViewController;

  @override
  void initState() {
    final matchId = context.read<HomeBloc>().state.game.currentMatch?.id ?? 0;

    _webViewController =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0xffffffff))
          ..addJavaScriptChannel(
            "eventSector",
            onMessageReceived: (JavaScriptMessage message) {
              // Handle events received from JavaScript
              String event = message.message;
              context.read<OrderBloc>().add(GetSectorDataEvent(sector: event, matchId: matchId));

              showModalBottomSheet(
                context: context,
                showDragHandle: true,
                elevation: 0,
                backgroundColor: AppColors.white,
                builder: (context) {
                  return WSectorModalPopup(message: event, isId: true);
                },
              );
            },
          )
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {},
              onPageStarted: (String url) {},
              onPageFinished: (String url) {},
              onHttpError: (HttpResponseError error) {
                log(error.toString());
              },
              onWebResourceError: (WebResourceError error) {
                log(error.toString());
              },
              onNavigationRequest: (NavigationRequest request) {
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(Uri.parse('https://select-ticket.echipta.uz/'));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Sektor tanlash", style: context.textTheme.displayMedium)),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "2025 yilgi mavsum uchun joyingizni tanlang",
              textAlign: TextAlign.center,
              style: context.textTheme.bodyLarge,
            ),
            const Gap(20),
            Expanded(child: WebViewWidget(controller: _webViewController)),
            SizedBox(
              width: double.maxFinite,
              child: Wrap(
                spacing: 10,
                crossAxisAlignment: WrapCrossAlignment.center,
                runAlignment: WrapAlignment.center,
                alignment: WrapAlignment.center,
                runSpacing: 10,
                children: List.generate(sectorTypes.length, (index) {
                  final item = sectorTypes[index];
                  return WSectorTypeItem(type: item);
                }),
              ),
            ),
            Gap(context.padding.bottom),
          ],
        ),
      ),
    );
  }
}
