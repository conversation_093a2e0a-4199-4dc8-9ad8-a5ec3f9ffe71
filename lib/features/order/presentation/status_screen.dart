import 'dart:async';

import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class StatusScreen extends StatefulWidget {
  final int orderId;
  const StatusScreen({super.key, required this.orderId});

  @override
  State<StatusScreen> createState() => _StatusScreenState();
}

class _StatusScreenState extends State<StatusScreen> {
  Timer? _timer;
  @override
  void initState() {
    super.initState();
    _startApiCall();
  }

  void _startApiCall() {
    // Make initial call immediately
    context.read<OrderBloc>().add(
      GetOrderStatusEvent(orderId: widget.orderId),
    );

    // Then start periodic polling
    _timer = Timer.periodic(Duration(seconds: 5), (timer) {
      context.read<OrderBloc>().add(
        GetOrderStatusEvent(orderId: widget.orderId),
      );
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF9FAFB),
      body: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          final status = state.orderInfo;
          return Padding(
            padding: const EdgeInsets.all(20.0),
            child: Center(
              child: Ink(
                padding: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      status == 1
                          ? AppAssets.wait
                          : status == 2
                          ? AppAssets.success
                          : status == 0
                          ? AppAssets.wait  // Show waiting for initial/loading state
                          : AppAssets.error,
                      width: MediaQuery.of(context).size.width * 0.5,
                    ),
                    Gap(18),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(18),
                        color:
                            status == 1
                                ? AppColors.yellow3
                                : status == 2
                                ? AppColors.green3
                                : status == 0
                                ? AppColors.yellow3  // Show yellow for initial/loading state
                                : AppColors.red3,
                      ),
                      child: Text(
                        status == 1
                            ? "To‘lov jarayonda"
                            : status == 2
                            ? "To‘lov tasdiqlandi"
                            : status == 0
                            ? "To'lov tekshirilmoqda"
                            : "To‘lov rad etildi",
                        style: context.textTheme.bodyLarge!.copyWith(
                          color:
                              status == 1
                                  ? AppColors.yellow
                                  : status == 2
                                  ? AppColors.green
                                  : status == 0
                                  ? AppColors.yellow  // Show yellow for initial/loading state
                                  : AppColors.red,
                        ),
                      ),
                    ),
                    Gap(20),
                    Text(
                      state.orderInfo == 1
                          ? "To‘lov jarayonda tekshirib ko‘ring"
                          : state.orderInfo == 2
                          ? "To‘lov qabul qilindi"
                          : state.orderInfo == 0
                          ? "To'lov holatini tekshirmoqdamiz"
                          : "To‘lov qilishda xatolik yuz berdi :(",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Gap(20),
                    Text(
                      state.orderInfo == 1
                          ? "To’lov amalga oshirish jarayonida. Iltimos kuting"
                          : state.orderInfo == 2
                          ? "Chiptangiz ID kartangizga biriktirildi. Uchrashuv kuni ID kartangiz orqali kirishingiz mumkin"
                          : state.orderInfo == 0
                          ? "To'lov holatini tekshirmoqdamiz. Iltimos kuting"
                          : "Iltimos hisobingizni tekshiring yoki qaytadan harakat qilib ko`ring",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.darkGrey,
                      ),
                    ),
                    Gap(30),
                    WButton(
                      onTap: () {
                        switch (status) {
                          case 0:
                          case 1:
                            context.read<OrderBloc>().add(
                              GetOrderStatusEvent(orderId: widget.orderId),
                            );
                          case 2:
                            context.go(AppRouter.navigator);
                          default:
                            Navigator.pop(context);
                        }
                      },
                      txt:
                          status == 1
                              ? "Tekshirish"
                              : status == 2
                              ? "Chiptani ko’rish"
                              : status == 0
                              ? "Tekshirish"
                              : "Qayta urinish",
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
