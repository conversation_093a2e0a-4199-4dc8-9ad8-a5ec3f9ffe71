import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';

class WBillInformationItem extends StatelessWidget {
  const WBillInformationItem(
      {super.key, required this.title, required this.subtitle});
  final String title;
  final String subtitle;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(child: Text(title, style: context.textTheme.headlineMedium)),
        Flexible(child: Text(subtitle, style: context.textTheme.headlineMedium)),
      ],
    );
  }
}
