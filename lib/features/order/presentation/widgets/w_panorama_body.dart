import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:panorama_viewer/panorama_viewer.dart';

class WPanoramaBody extends StatelessWidget {
  const WPanoramaBody({super.key, this.isId = false});
  final bool? isId;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        return Stack(
          children: [
            PanoramaViewer(child: Image.network(state.ticket.panorama)),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                height: 200,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.primary.withOpacity(0),
                      AppColors.primary,
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(30.0),
              child: Image.asset(AppAssets.radius, width: 70),
            ),
            Positioned(
              bottom: 0,
              right: 50,
              left: 50,
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        children: [
                          Text(
                            LocaleKeys.sector.tr(),
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: AppColors.white,
                            ),
                          ),
                          Text(
                            state.ticket.sector,
                            style: Theme.of(
                              context,
                            ).textTheme.displayMedium!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                      const Gap(20),
                      Container(
                        width: 2,
                        height: 37,
                        color: AppColors.darkGrey,
                      ),
                      const Gap(20),
                      Column(
                        children: [
                          Text(
                            LocaleKeys.row.tr(),
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: AppColors.white,
                            ),
                          ),
                          Text(
                            state.ticket.row,
                            style: Theme.of(
                              context,
                            ).textTheme.displayMedium!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                      const Gap(20),
                      Container(
                        width: 2,
                        height: 37,
                        color: AppColors.darkGrey,
                      ),
                      const Gap(20),
                      Column(
                        children: [
                           Text(
                            LocaleKeys.seat.tr(),
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: AppColors.white,
                            ),
                          ),
                          Text(
                            state.ticket.seat,
                            style: Theme.of(
                              context,
                            ).textTheme.displayMedium!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Text(
                    LocaleKeys.pov.tr(),
                    style: Theme.of(
                      context,
                    ).textTheme.labelLarge!.copyWith(color: AppColors.white),
                  ),
                  SvgPicture.asset(AppAssets.circle),
                  const Gap(30),
                  ElevatedButton(
                    onPressed: () {
                      if (isId ?? false) {
                        context.push(AppRouter.orderid);
                      } else {
                        context.push(AppRouter.order);
                      }
                    },
                    style: const ButtonStyle(
                      minimumSize: WidgetStatePropertyAll(
                        Size(double.maxFinite, 56),
                      ),
                      backgroundColor: WidgetStatePropertyAll(AppColors.white),
                      elevation: WidgetStatePropertyAll(0),
                    ),
                    child:  Text(
                      LocaleKeys.endPay.tr(),
                      style: TextStyle(color: AppColors.primary),
                    ),
                  ),
                  Gap(MediaQuery.of(context).viewPadding.bottom + 20),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
