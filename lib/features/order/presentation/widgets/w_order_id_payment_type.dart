// ignore_for_file: deprecated_member_use

import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class WOrderIdPaymentType extends StatelessWidget {
  const WOrderIdPaymentType({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "To'lov usulini tanlang",
          style: context.textTheme.displaySmall,
        ),
        const Gap(20),
        BlocBuilder<ProfileBloc, ProfileState>(
          builder: (context, state) {
            return ListTile(
              onTap: () {
                context.read<OrderBloc>().add(const SelectOrderPaymentTypeEvent(
                    paymentType: OrderPaymentType.balance));
              },
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(100)),
              tileColor: AppColors.primary3,
              leading: const CircleAvatar(
                backgroundColor: AppColors.primary,
                child: Icon(
                  Icons.person,
                  color: AppColors.white,
                ),
              ),
              trailing: BlocBuilder<OrderBloc, OrderState>(
                builder: (context, state) {
                  return Icon(
                    state.paymentType == OrderPaymentType.balance
                        ? Icons.check_circle
                        : Icons.circle_outlined,
                    color: AppColors.primary,
                    size: 42,
                  );
                },
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              title: const Text("Profil balansi orqali to`lov"),
              subtitle: Text(
                  "${state.me.balance.toDouble().formatAsSpaceSeparated()} so`m"),
            );
          },
        ),
        const Gap(16),
        ListTile(
          onTap: () {
            context.read<OrderBloc>().add(const SelectOrderPaymentTypeEvent(
                paymentType: OrderPaymentType.alif));
          },
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(100)),
          tileColor: AppColors.primary3,
          leading: CircleAvatar(
              backgroundColor: AppColors.primary,
              child: SvgPicture.asset(
                AppAssets.alif,
                color: AppColors.white,
                width: 28,
              )),
          trailing: BlocBuilder<OrderBloc, OrderState>(
            builder: (context, state) {
              return Icon(
                state.paymentType == OrderPaymentType.alif
                    ? Icons.check_circle
                    : Icons.circle_outlined,
                color: AppColors.primary,
                size: 42,
              );
            },
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16),
          title: const Text("Alif Pay orqali to'lov"),
        ),
      ],
    );
  }
}
