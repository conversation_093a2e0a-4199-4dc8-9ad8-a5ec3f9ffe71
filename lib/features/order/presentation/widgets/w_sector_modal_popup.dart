import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/order/domain/entities/sector_type_entity.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_sector_info_item.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class WSectorModalPopup extends StatelessWidget {
  const WSectorModalPopup({super.key, required this.message, this.isId = false});

  final String message;
  final bool? isId;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state.sectorStatus.isInProgress) {
          return const Center(child: CircularProgressIndicator.adaptive());
        } else if (state.sectorStatus.isSuccess) {
          final type = sectorTypes.firstWhere(
            (element) => element.title.toUpperCase() == state.sector.type.toUpperCase(),
          );
          final sector = state.sector;
          return SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                WSectorInfoItem(message: message, type: type, sector: sector),
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
                  child: Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            LocaleKeys.price2.tr(),
                            style: context.textTheme.headlineLarge!.copyWith(color: AppColors.darkGrey),
                          ),
                          Text(
                            sector.price,
                            style: context.textTheme.displayMedium!.copyWith(fontWeight: FontWeight.w700),
                          ),
                        ],
                      ),
                      const Gap(20),
                      Expanded(
                        child: WButton(
                          onTap: () {
                            if (isId ?? false) {
                              context.pushReplacement(AppRouter.idseat, extra: message);
                            } else {
                              context.pop();
                              context.pushNamed("seat", pathParameters: {"sector": message});
                            }
                          },
                          txt: LocaleKeys.select.tr(),
                        ),
                      ),
                    ],
                  ),
                ),
                Gap(context.padding.bottom),
              ],
            ),
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }
}
