import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WErrorModalPopup extends StatelessWidget {
  const WErrorModalPopup({
    super.key,
    this.title,
    this.message,
    this.buttonText = "Qayta urinish",
    this.onButtonTap,
  });

  final String? title;
  final String? message;
  final String buttonText;
  final VoidCallback? onButtonTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              AppAssets.error,
              width: MediaQuery.of(context).size.width * 0.2,
            ),
            const Gap(20),
            Text(
              title ?? "To'lov qilishda xatolik yuz berdi :(",
              textAlign: TextAlign.center,
              style: context.textTheme.displayLarge,
            ),
            const Gap(10),
            Text(
              message ?? "Iltimos hisobingizni tekshiring yoki qaytadan harakat qilib ko'ring",
              textAlign: TextAlign.center,
              style: context.textTheme.bodySmall!.copyWith(
                color: AppColors.darkGrey,
              ),
            ),
            const Gap(20),
            WButton(
              onTap: onButtonTap ?? () => Navigator.of(context).pop(),
              txt: buttonText,
            ),
            Gap(context.padding.bottom),
          ],
        ),
      ),
    );
  }
}
