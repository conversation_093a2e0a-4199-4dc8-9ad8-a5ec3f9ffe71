import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WSeatTypes extends StatelessWidget {
  const WSeatTypes({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Row(
          children: [
            Container(height: 18, width: 18, color: const Color(0xff395097)),
            const Gap(5),
            Text(
              LocaleKeys.emptySeats2.tr(),
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        Row(
          children: [
            Container(height: 18, width: 18, color: const Color(0xffEB3B30)),
            const Gap(5),
            Text(
              LocaleKeys.notEmptyPlaces.tr(),
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        Row(
          children: [
            Container(height: 18, width: 18, color: AppColors.green),
            const Gap(5),
            Text(
              LocaleKeys.selected.tr(),
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ],
        ),
      ],
    );
  }
}
