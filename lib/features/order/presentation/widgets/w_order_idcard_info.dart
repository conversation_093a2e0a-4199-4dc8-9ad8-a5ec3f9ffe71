import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/gift/presentation/bloc/gift_bloc.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_bill_information_item.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class WOrderIdCardInfo extends StatelessWidget {
  const WOrderIdCardInfo({super.key, required int start}) : _start = start;

  final int _start;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        final ticket = state.ticket;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("To'lov tafsilotlari", style: context.textTheme.displaySmall),
            const Gap(20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.primary3,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  BlocBuilder<ProfileBloc, ProfileState>(
                    builder: (context, state) {
                      return WBillInformationItem(
                        title: "O'yin:",
                        subtitle:
                            state.idCardType.name == "simple"
                                ? "Oddiy ID karta"
                                : "Mavsumiy ID karta",
                      );
                    },
                  ),
                  const Gap(10),
                  WBillInformationItem(
                    title: "Sektor:",
                    subtitle: ticket.sector,
                  ),
                  const Gap(10),
                  WBillInformationItem(title: "Qator:", subtitle: ticket.row),
                  const Gap(10),
                  WBillInformationItem(title: "Joy", subtitle: ticket.seat),
                  const Gap(10),
                  WBillInformationItem(
                    title: "Chipta narxi:",
                    subtitle: ticket.price,
                  ),
                  const Gap(10),
                  WBillInformationItem(
                    title: "Chipta sotib olish vaqti:",
                    subtitle: _start.formatTime(),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
