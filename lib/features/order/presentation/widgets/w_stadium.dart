import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/home/<USER>/entities/match_stadium_entity.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:url_launcher/url_launcher.dart' as launcher;

class WStadium extends StatelessWidget {
  const WStadium({super.key, required this.stadium});

  final MatchStadiumEntity stadium;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.65,
      padding: EdgeInsets.fromLTRB(20, 20, 20, 20 + context.padding.bottom),
      width: double.maxFinite,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "${stadium.name} stadioni",
            style: context.textTheme.displaySmall,
          ),
          Gap(10),
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: CachedNetworkImage(
              fit: BoxFit.cover,
              imageUrl:
                  "https://static-maps.yandex.ru/1.x/?ll=${stadium.longitude},${stadium.latitude}&z=16&size=600,450&l=map&pt=${stadium.longitude},${stadium.latitude},pm2rdl",
              errorWidget: (context, url, error) {
                return Placeholder();
              },
              height: 150,
              width: double.maxFinite,
            ),
          ),
          Gap(10),
          Text(stadium.address, style: context.textTheme.bodyLarge),
          Gap(10),
          Text(
            "Stadion sig'imi: ${stadium.count_sectors} dona",
            style: context.textTheme.bodyLarge,
          ),
          Gap(20),
          WButton(
            onTap: () async {
              Uri url = Uri(
                scheme: "geo",
                path: "${stadium.latitude},${stadium.longitude}",
              );

              // Check if the URL can be launched
              if (await launcher.launchUrl(url)) {
              } else {
                // Show an error if the URL can't be launched
                throw 'Could not launch ';
              }
            },
            txt: "Yo'nalish olish",
          ),
          Gap(10),
          Expanded(
            child: ListView.separated(
              separatorBuilder: (context, index) => Gap(10),
              itemCount: stadium.images.length,
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                final item = stadium.images[index];
                return ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    fit: BoxFit.cover,
                    imageUrl: item.image,
                    errorWidget: (context, url, error) {
                      return Placeholder();
                    },
                    height: 150,
                    width: 150,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
