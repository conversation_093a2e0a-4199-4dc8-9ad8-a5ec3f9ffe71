import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/entities/sector_entity.dart';
import 'package:echipta/features/order/domain/entities/sector_type_entity.dart';
import 'package:echipta/features/home/<USER>/widgets/w_sector_type_item.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WSectorInfoItem extends StatelessWidget {
  const WSectorInfoItem({
    super.key,
    required this.message,
    required this.type,
    required this.sector,
  });

  final String message;
  final SectorTypeEntity type;
  final SectorEntity sector;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 83,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16), color: AppColors.fillColor),
      margin: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              Text(
                LocaleKeys.sector.tr(),
                style: context.textTheme.labelSmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                message,
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
          const Gap(16),
          const VerticalDivider(
            thickness: 2,
          ),
          const Gap(16),
          Column(
            children: [
              Text(
                LocaleKeys.type.tr(),
                style: context.textTheme.labelSmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              WSectorTypeItem(
                type: type,
              ),
            ],
          ),
          const Gap(16),
          const VerticalDivider(
            thickness: 2,
          ),
          const Gap(16),
          Column(
            children: [
              Text(
                LocaleKeys.emptyPlaces.tr(),
                style: context.textTheme.labelSmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                sector.free_seats_count.toString(),
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
        ],
      ),
    );
  }
}
