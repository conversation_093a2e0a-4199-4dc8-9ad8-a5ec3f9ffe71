import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_seat_info_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class WSeatIdModalPopup extends StatelessWidget {
  const WSeatIdModalPopup({super.key, required this.data, required this.sector});
  final dynamic data;
  final String sector;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state.ticketStatus.isSuccess) {
          return SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                WSeatInfoItem(data: data, sector: sector),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: WButton(
                    onTap: () {
                      context.pop();
                      context.push(AppRouter.panorama, extra: true);
                    },
                    txt: "Joyni ko‘rish",
                  ),
                ),
                Gap(20 + context.padding.bottom),
              ],
            ),
          );
        } else {
          return const Center(child: CircularProgressIndicator.adaptive());
        }
      },
    );
  }
}
