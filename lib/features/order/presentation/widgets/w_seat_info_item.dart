import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

class WSeatInfoItem extends StatelessWidget {
  const WSeatInfoItem({
    super.key,
    required this.data,
    required this.sector,
  });

  final dynamic data;
  final String sector;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 83,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16), color: AppColors.fillColor),
      margin: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              Text(
                "Sektor",
                style: context.textTheme.labelSmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                sector,
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
          const Gap(16),
          const VerticalDivider(
            thickness: 2,
          ),
          const Gap(16),
          Column(
            children: [
              Text(
                "Qator",
                style: context.textTheme.labelSmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                data["row"],
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
          const Gap(16),
          const VerticalDivider(
            thickness: 2,
          ),
          const Gap(16),
          Column(
            children: [
              Text(
                "Bosh joylar soni",
                style: context.textTheme.labelSmall!.copyWith(
                    color: AppColors.darkGrey, fontWeight: FontWeight.w600),
              ),
              Text(
                data["seat"],
                style: context.textTheme.displayMedium!
                    .copyWith(fontWeight: FontWeight.w700),
              )
            ],
          ),
        ],
      ),
    );
  }
}
