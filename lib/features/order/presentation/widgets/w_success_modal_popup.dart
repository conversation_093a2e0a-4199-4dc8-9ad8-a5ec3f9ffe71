import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class WSuccessModalPopup extends StatelessWidget {
  const WSuccessModalPopup({
    super.key,
    this.txt =
        "Chiptangiz ID kartangizga biriktirildi. Uchrashuv kuni ID kartangiz orqali kirishingiz mumkin",
    this.title = "To'lov qabul qilindi",
    this.buttonText = "Chiptani ko'rish",
    this.onButtonTap,
  });
  final String? txt;
  final String? title;
  final String buttonText;
  final VoidCallback? onButtonTap;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              AppAssets.success,
              width: MediaQuery.of(context).size.width * 0.2,
            ),
            const Gap(20),
            Text(title!, style: context.textTheme.displayLarge),
            const Gap(10),
            Text(
              txt!,
              textAlign: TextAlign.center,
              style: context.textTheme.bodySmall!.copyWith(
                color: AppColors.darkGrey,
              ),
            ),
            const Gap(20),
            WButton(
              onTap: onButtonTap ?? () {
                context.pushReplacement(AppRouter.tickets);
              },
              txt: buttonText,
            ),
            Gap(context.padding.bottom),
          ],
        ),
      ),
    );
  }
}
