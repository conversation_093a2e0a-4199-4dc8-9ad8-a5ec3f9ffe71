import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/home/<USER>/widgets/w_sector_row_seat.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class WSeatModalPopup extends StatelessWidget {
  const WSeatModalPopup({super.key, required this.data, required this.sector});
  final dynamic data;
  final String sector;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state.ticketStatus.isSuccess) {
          final ticket = state.ticket;
          final match = state.selectedMatch;
          return SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      match.main_team.name,
                      style: context.textTheme.titleLarge!.copyWith(color: AppColors.darkGrey),
                    ),
                    const Gap(10),
                    CachedNetworkImage(imageUrl: match.main_team.image, width: 50, height: 50),
                    const Gap(10),
                    Text("vs", style: context.textTheme.titleLarge!.copyWith(color: AppColors.darkGrey)),
                    const Gap(10),
                    CachedNetworkImage(imageUrl: match.second_team.image, width: 50, height: 50),
                    const Gap(10),
                    Text(
                      match.second_team.name,
                      style: context.textTheme.titleLarge!.copyWith(color: AppColors.darkGrey),
                    ),
                  ],
                ),
                const Gap(20),
                const Padding(padding: EdgeInsets.symmetric(horizontal: 20), child: Divider(thickness: 2, height: 0)),
                const Gap(10),
                WSectorRowSeat(sector: sector, row: data["row"], seat: data["seat"]),
                const Gap(10),
                const Padding(padding: EdgeInsets.symmetric(horizontal: 20), child: Divider(thickness: 2, height: 0)),
                const Gap(20),
                Text(ticket.price, style: context.textTheme.displayLarge!.copyWith(fontSize: 32)),
                const Gap(20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: WButton(
                    onTap: () {
                      context.push(AppRouter.panorama, extra: false);
                    },
                    txt: LocaleKeys.viewPlace.tr(),
                  ),
                ),
                Gap(20 + context.padding.bottom),
              ],
            ),
          );
        } else if (state.ticketStatus.isInProgress) {
          return const Center(child: CircularProgressIndicator.adaptive());
        } else {
          return const SizedBox();
        }
      },
    );
  }
}
