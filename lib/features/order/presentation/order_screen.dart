// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_error_modal.dart';
import 'package:echipta/features/gift/presentation/bloc/gift_bloc.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_order_info.dart';
import 'package:echipta/features/order/presentation/widgets/w_order_payment.dart';
import 'package:echipta/features/order/presentation/widgets/w_success_modal_popup.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderScreen extends StatefulWidget {
  const OrderScreen({super.key});

  @override
  State<OrderScreen> createState() => _OrderScreenState();
}

class _OrderScreenState extends State<OrderScreen> {
  late Timer _timer;
  int _start = 600; // Starting value of the countdown in seconds

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_start > 0) {
        setState(() {
          _start--;
        });
      } else {
        _timer.cancel(); // Stop the timer when countdown reaches 0
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        surfaceTintColor: AppColors.primary,
        foregroundColor: AppColors.white,
        title: Text(
          LocaleKeys.pay.tr(),
          style: context.textTheme.displayMedium!.copyWith(
            color: AppColors.white,
          ),
        ),
      ),
      body: BlocBuilder<OrderBloc, OrderState>(
        builder: (context, state) {
          return ListView(
            padding: const EdgeInsets.all(20),
            children: [
              WOrderInfo(start: _start),
              const Gap(20),
              const WOrderPayment(),
              const Gap(40),
              WButton(
                onTap: () {
                  context.read<OrderBloc>().add(
                    OrderTicketEvent(
                      paymentType: state.paymentType.name,
                      onSuccess: (response) async {
                        print("🔍 Order Success Response: $response");
                        print("🔍 Response Type: ${response.runtimeType}");
                        print("🔍 Payment Type: ${state.paymentType}");

                        // Handle new response structure
                        if (response is Map<String, dynamic>) {
                          if (response["error"] == true) {
                            // Handle error cases
                            String errorMessage =
                                response["message"] ?? "Unknown error";
                            String title;

                            if (errorMessage == "Balance not enough") {
                              title = "Balansingizda yetarli mablag' yo'q!";
                            } else {
                              // Show the exact server message
                              title = errorMessage;
                            }

                            showModalBottomSheet(
                              context: context,
                              backgroundColor: AppColors.white,
                              elevation: 0,
                              builder: (context) {
                                return WErrorModal(title: title);
                              },
                            );
                          } else {
                            // Handle success cases
                            if (response["message"] ==
                                "Muvaffaqiyatli to'landi") {
                              // Show success bottom sheet
                              showModalBottomSheet(
                                context: context,
                                backgroundColor: AppColors.white,
                                elevation: 0,
                                isDismissible: false,
                                enableDrag: false,
                                builder: (context) {
                                  // Auto close after 3 seconds and navigate to main
                                  Future.delayed(
                                    const Duration(seconds: 5),
                                    () {
                                      if (Navigator.canPop(context)) {
                                        Navigator.pop(context);
                                      }
                                      context.go(AppRouter.navigator);
                                    },
                                  );

                                  return WSuccessModalPopup(
                                    title:
                                        state.orderType == OrderType.gift
                                            ? "🎉 Sovg'angiz yetkazildi! 🎊"
                                            : "Muvaffaqiyatli to'landi",
                                    txt:
                                        state.orderType == OrderType.gift
                                            ? "Sizning sovg'angiz muvaffaqiyatli yetkazildi! Do'stingiz tez orada xabar oladi."
                                            : "To'lovingiz muvaffaqiyatli amalga oshirildi. Chiptangiz tayyor!",
                                    buttonText:
                                        state.orderType == OrderType.gift
                                            ? "Asosiy oynaga qaytish"
                                            : "Chiptani ko'rish",
                                    onButtonTap:
                                        state.orderType == OrderType.gift
                                            ? () {
                                              Navigator.pop(context);
                                              context.go(AppRouter.navigator);
                                            }
                                            : null, // Use default behavior for regular payments
                                  );
                                },
                              );
                            } else if (response["data"] != null) {
                              // Handle payment data (Alif payment case)
                              final data = response["data"];
                              print("🔍 Payment data: $data");
                              print("🔍 Order ID: ${data["order_id"]}");
                              print("🔍 Payment URL: ${data["payment_url"]}");
                              print(
                                "🔍 Is Alif payment: ${state.paymentType == OrderPaymentType.alif}",
                              );

                              if (state.paymentType == OrderPaymentType.alif &&
                                  data["order_id"] != null) {
                                final String paymentUrl = data["payment_url"] ?? "";
                                print("🔍 Payment URL: $paymentUrl");

                                if (paymentUrl.isNotEmpty) {
                                  try {
                                    final Uri url = Uri.parse(paymentUrl);
                                    print("🔍 Parsed URL: $url");

                                    // Try different launch modes for better compatibility
                                    bool launched = false;

                                    // First try: InAppWebView
                                    try {
                                      launched = await launchUrl(url, mode: LaunchMode.inAppWebView);
                                      print("✅ URL launched with InAppWebView: $launched");
                                    } catch (e) {
                                      print("❌ InAppWebView failed: $e");
                                    }

                                    // Second try: External browser if InAppWebView failed
                                    if (!launched) {
                                      try {
                                        launched = await launchUrl(url, mode: LaunchMode.externalApplication);
                                        print("✅ URL launched with external browser: $launched");
                                      } catch (e) {
                                        print("❌ External browser failed: $e");
                                      }
                                    }

                                    // Third try: Platform default
                                    if (!launched) {
                                      try {
                                        launched = await launchUrl(url);
                                        print("✅ URL launched with platform default: $launched");
                                      } catch (e) {
                                        print("❌ Platform default failed: $e");
                                      }
                                    }

                                    if (launched) {
                                      print("✅ URL launched successfully, will navigate to status after user returns");
                                      // Store order ID for later navigation
                                      final orderId = data["order_id"];

                                      // Wait a bit for the webview to open, then navigate to status
                                      Future.delayed(Duration(seconds: 2), () {
                                        if (mounted) {
                                          context.push(AppRouter.orderStatus, extra: orderId);
                                        }
                                      });
                                    } else {
                                      print("❌ All URL launch methods failed");
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                          content: Text("To'lov sahifasini ochishda xatolik yuz berdi"),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                    }

                                  } catch (e) {
                                    print("❌ URL parsing failed: $e");
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text("Noto'g'ri to'lov havolasi"),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                } else {
                                  print("❌ Empty payment URL");
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text("To'lov havolasi topilmadi"),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              } else {
                                print("❌ Alif payment condition not met");
                                print(
                                  "   - Payment type: ${state.paymentType}",
                                );
                                print(
                                  "   - Order ID exists: ${data["order_id"] != null}",
                                );
                              }
                            } else {
                              print("❌ No data in response");
                            }
                          }
                        } else {
                          print("❌ Response is not a Map<String, dynamic>");
                          print("   Response: $response");
                          print("   Type: ${response.runtimeType}");
                        }
                      },
                      onError: (p0) {},
                      userForGift:
                          state.orderType == OrderType.gift
                              ? context
                                  .read<GiftBloc>()
                                  .state
                                  .selectedUser
                                  .id
                                  .toString()
                              : null,
                    ),
                  );
                },
                txt: LocaleKeys.endPay.tr(),
              ),
              const Gap(10),
              WButton(
                btnColor: AppColors.primary2,
                onTap: () {
                  context.go(AppRouter.navigator);
                },
                txt: "Asosiy oynaga qaytish",
              ),
            ],
          );
        },
      ),
    );
  }
}
