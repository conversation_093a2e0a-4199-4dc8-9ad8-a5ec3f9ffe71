import 'package:echipta/features/order/domain/entities/order_history_entity.dart';
import 'package:echipta/features/order/domain/entities/order_items_entity.dart';
import 'package:echipta/features/profile/domain/entities/my_ticket_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'order_history_model.g.dart';

@JsonSerializable()
class OrderHistoryModel extends OrderHistoryEntity {
  const OrderHistoryModel({
    super.id,
    super.type,
    super.amount,
    super.ticket,
    super.items,
  });

  factory OrderHistoryModel.fromJson(Map<String, dynamic> json) =>
      _$OrderHistoryModelFromJson(json);
}
