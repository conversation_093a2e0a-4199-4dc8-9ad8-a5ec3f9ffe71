// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_history_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderHistoryModel _$OrderHistoryModelFromJson(Map<String, dynamic> json) =>
    OrderHistoryModel(
      id: (json['id'] as num?)?.toInt() ?? 0,
      type: (json['type'] as num?)?.toInt() ?? -1,
      amount: (json['amount'] as num?)?.toInt() ?? 0,
      ticket: _$JsonConverterFromJson<Map<String, dynamic>, MyTicketEntity>(
        json['ticket'],
        const MyTicketConverter().fromJson,
      ),
      items:
          (json['items'] as List<dynamic>?)
              ?.map(
                (e) => const OrderItemsConverter().fromJson(
                  e as Map<String, dynamic>,
                ),
              )
              .toList(),
    );

Map<String, dynamic> _$OrderHistoryModelToJson(OrderHistoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'amount': instance.amount,
      'ticket': _$JsonConverterToJson<Map<String, dynamic>, MyTicketEntity>(
        instance.ticket,
        const MyTicketConverter().toJson,
      ),
      'items': instance.items?.map(const OrderItemsConverter().toJson).toList(),
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) => json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) => value == null ? null : toJson(value);
