import 'package:echipta/features/home/<USER>/entities/product_entity.dart';
import 'package:echipta/features/order/domain/entities/order_items_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'order_items_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class OrderItemsModel extends OrderItemsEntity {
  const OrderItemsModel({
    super.item_id,
    super.product,
  });

  factory OrderItemsModel.fromJson(Map<String, dynamic> json) =>
      _$OrderItemsModelFromJson(json);
}
