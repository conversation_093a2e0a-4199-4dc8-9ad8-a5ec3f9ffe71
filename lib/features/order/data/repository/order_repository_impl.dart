import 'package:dio/dio.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/home/<USER>/entities/sector_entity.dart';
import 'package:echipta/features/home/<USER>/entities/ticket_entity.dart';
import 'package:echipta/features/order/data/datasource/order_datasource.dart';
import 'package:echipta/features/order/domain/entities/order_history_entity.dart';
import 'package:echipta/features/order/domain/repository/order_repository.dart';

class OrderRepositoryImpl implements OrderRepository {
  final OrderDatasource _datasource = serviceLocator<OrderDatasourceImpl>();
  @override
  Future<Either<Failure, Map<String, dynamic>>> ticketPayment(
    OrderParams params,
  ) async {
    try {
      final result = await _datasource.ticketPayment(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, SectorEntity>> getSectorData(
    SectorParams params,
  ) async {
    try {
      final result = await _datasource.getSectorData(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, TicketEntity>> getTicketInfo(
    OrderParams params,
  ) async {
    try {
      final result = await _datasource.getTicketInfo(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, List<OrderHistoryEntity>>> getOrderHistory() async {
    try {
      final result = await _datasource.getOrderHistory();
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, dynamic>> orderIdCard(OrderParams params) async {
    try {
      final result = await _datasource.orderIdCard(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, dynamic>> getOrderStatus(IdParam params) async {
    try {
      final result = await _datasource.getOrderStatus(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(
        ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey,
        ),
      );
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }
}
