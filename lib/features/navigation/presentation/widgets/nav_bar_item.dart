import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/navigation/domain/entities/navbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class NavItemWidget extends StatelessWidget {
  final int currentIndex;
  final String? avatar;
  final NavBar navBar;
  final Function() onTap;

  const NavItemWidget({
    required this.navBar,
    required this.currentIndex,
    this.avatar,
    required this.onTap,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Expanded(
        child: GestureDetector(
          onTap: onTap,
          behavior: HitTestBehavior.opaque,
          child: Column(
            children: [
              currentIndex == navBar.id
                  ? Container(
                      decoration: const BoxDecoration(),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12).copyWith(top: 16),
                        child: SvgPicture.asset(
                          navBar.selectedIcon,
                          color: AppColors.primary,
                          height: 24,
                          width: 24,
                        ),
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12).copyWith(top: 16),
                      child: SvgPicture.asset(
                        navBar.defIcon,
                        height: 24,
                        width: 24,
                        color: AppColors.darkGrey,
                      ),
                    ),
              const SizedBox(
                height: 4,
              ),
              Container(
                alignment: Alignment.bottomCenter,
                child: Text(navBar.title.tr(),
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(
                          fontSize: 11,
                          color: currentIndex == navBar.id ? AppColors.black : AppColors.darkGrey,
                          fontWeight: currentIndex == navBar.id ? FontWeight.w700 : FontWeight.w500,
                        )),
              ),
            ],
          ),
        ),
      );
}
