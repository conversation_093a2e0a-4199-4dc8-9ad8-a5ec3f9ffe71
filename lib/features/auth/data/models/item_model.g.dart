// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ItemModel _$ItemModelFromJson(Map<String, dynamic> json) => ItemModel(
  id: (json['id'] as num?)?.toInt() ?? 0,
  image: json['image'] as String? ?? "",
  name: json['name'] as String? ?? "",
  title: json['title'] as String? ?? "",
  matches:
      (json['matches'] as List<dynamic>?)
          ?.map(
            (e) => const MatchConverter().fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
);

Map<String, dynamic> _$ItemModelToJson(ItemModel instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'image': instance.image,
  'title': instance.title,
  'matches': instance.matches.map(const MatchConverter().toJson).toList(),
};
