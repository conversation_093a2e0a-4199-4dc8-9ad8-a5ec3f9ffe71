import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/auth/data/models/item_model.dart';

abstract class AuthDatasource {
  Future<void> auth(AuthParams params);
  Future<bool> verifyAuth(AuthParams params);
  Future<void> register(UserParams params);
  Future<List<ItemModel>> getTeams();
  Future<void> updateTeam(IdParam params);
}

class AuthDatasourceImpl implements AuthDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  @override
  Future<void> auth(AuthParams params) async {
    try {
      final data = {"phone": "998${params.phoneNumber.joinSymbols()}"};
      final response = await _dio.post("/clients/get-otp", data: data);
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return;
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<bool> verifyAuth(AuthParams params) async {
    try {
      final data = {"phone": "998${params.phoneNumber.joinSymbols()}", "code": params.code};
      final response = await _dio.post("/clients/verify-otp", data: data);
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        if ((response.data["data"]["client"]["is_filed"] as bool)) {
          await StorageRepository.putString(StoreKeys.token, "Bearer ${response.data["data"]["access_token"]}");
        }
        await StorageRepository.putString(StoreKeys.passToken, "Bearer ${response.data["data"]["access_token"]}");
        return (response.data["data"]["client"]["is_filed"] as bool);
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<void> register(UserParams params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.passToken);
      var data = FormData.fromMap({'full_name': params.fullName, 'birth_date': params.birthDate});
      if (params.avatar != null && params.avatar!.path.isNotEmpty) {
        var image = await MultipartFile.fromFile(params.avatar!.path, filename: params.avatar!.path);
        data.files.add(MapEntry('picture', image));
      }
      var response = await _dio.post(
        '/clients/update-personal-data',
        data: data,
        options: Options(headers: {"Authorization": token}),
      );

      if ((response.statusCode ?? 0) >= 200 && (response.statusCode ?? 0) < 300) {
        return;
      } else {
        throw CustomException(message: response.statusMessage ?? '', code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<List<ItemModel>> getTeams() async {
    try {
      final response = await _dio.get("/teams/all");
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List).map((e) => ItemModel.fromJson(e as Map<String, dynamic>)).toList();
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<void> updateTeam(IdParam params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);

      final data = {"team_id": params.id};
      final response = await _dio.post(
        "/clients/update-team",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return;
      } else {
        final message = (response.data as Map<String, dynamic>).values.toString().replaceAll(RegExp(r'[\[\]]'), '');
        throw CustomException(message: message, code: '${response.statusCode}');
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
