// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

import 'package:echipta/features/auth/data/models/item_model.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';

class ItemEntity extends Equatable {
  final int id;
  final String name;
  final String image;
  final String title;
  @MatchConverter()
  final List<MatchEntity> matches;

  const ItemEntity({
    this.id = 0,
    this.name = "",
    this.image = "",
    this.title = "",
    this.matches = const [],
  });

  @override
  List<Object?> get props => [
        id,
        name,
        image,
        title,
        matches,
      ];

  ItemEntity copyWith({
    int? id,
    String? name,
    String? image,
    String? title,
    List<MatchEntity>? matches,
  }) {
    return ItemEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      title: title ?? this.title,
      matches: matches ?? this.matches,
    );
  }
}

class ItemConverter extends JsonConverter<ItemEntity, Map<String, dynamic>> {
  @override
  ItemEntity fromJson(Map<String, dynamic> json) => ItemModel.fromJson(json);

  @override
  Map<String, dynamic> toJson(ItemEntity object) => {};
  const ItemConverter();
}
