import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/auth/data/repository/auth_repository_impl.dart';
import 'package:echipta/features/auth/domain/repository/auth_repository.dart';

class UpdateTeamUseCase extends UseCase<void, IdParam> {
  final AuthRepository _repository = serviceLocator<AuthRepositoryImpl>();

  @override
  Future<Either<Failure, void>> call(IdParam params) async {
    return await _repository.updateTeam(params);
  }
}
