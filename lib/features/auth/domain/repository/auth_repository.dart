import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/auth/domain/entities/item_entity.dart';

abstract class AuthRepository {
  Future<Either<Failure, void>> auth(AuthParams params);
  Future<Either<Failure, bool>> veirfyAuth(AuthParams params);
  Future<Either<Failure, void>> register(UserParams params);
  Future<Either<Failure, List<ItemEntity>>> getTeams();
  Future<Either<Failure, void>> updateTeam(IdParam params);
}
