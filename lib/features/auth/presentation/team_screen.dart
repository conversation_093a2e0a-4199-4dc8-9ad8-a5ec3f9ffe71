import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class TeamScreen extends StatefulWidget {
  const TeamScreen({super.key});

  @override
  State<TeamScreen> createState() => _TeamScreenState();
}

class _TeamScreenState extends State<TeamScreen> {
  @override
  void initState() {
    super.initState();
    context.read<AuthBloc>().add(GetTeamsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(centerTitle: true, title: Text(LocaleKeys.selectTeam.tr(), style: context.textTheme.displaySmall)),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state.teamsStatus.isInProgress) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }
          return Padding(
            padding: EdgeInsets.fromLTRB(20.0, 20, 20, 20 + context.padding.bottom),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: GridView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.teams.length,
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        mainAxisExtent: 126,
                      ),
                      itemBuilder: (context, index) {
                        final item = state.teams[index];
                        return BlocBuilder<ProfileBloc, ProfileState>(
                          builder: (context, userState) {
                            return GestureDetector(
                              onTap: () {
                                context.read<AuthBloc>().add(SelectTeamEvent(item));
                              },
                              child: Container(
                                padding: const EdgeInsets.all(16),
                                width: 150,
                                decoration: BoxDecoration(
                                  color:
                                      state.selectedTeam == item || userState.me.team == item
                                          ? AppColors.primary
                                          : AppColors.mediumGrey,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  children: [
                                    CachedNetworkImage(
                                      imageUrl: item.image,
                                      width: 50,
                                      errorWidget: (context, url, error) {
                                        return Icon(Icons.sports_soccer, color: AppColors.white, size: 60);
                                      },
                                    ),
                                    const Gap(6),
                                    Flexible(
                                      child: Text(
                                        item.name,
                                        style: context.textTheme.bodyLarge!.copyWith(
                                          color:
                                              state.selectedTeam == item || userState.me.team == item
                                                  ? AppColors.white
                                                  : AppColors.black,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                  const Spacer(),
                  WButton(
                    isLoading: state.updateTeamStatus.isInProgress,
                    onTap: () async {
                      context.read<AuthBloc>().add(
                        UpdateTeamEvent(
                          onError: (p0) {},
                          onSuccess: () async {
                            final pin = StorageRepository.getString(StoreKeys.pincode);
                            if (pin.isEmpty) {
                              context.go(AppRouter.setPincode);
                            } else {
                              Navigator.pop(context);
                            }
                          },
                        ),
                      );
                    },
                    txt: LocaleKeys.confirm.tr(),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
