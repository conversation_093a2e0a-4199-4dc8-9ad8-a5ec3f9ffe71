import 'dart:async';

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_snackbar.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:pinput/pinput.dart';

class VerificationScreen extends StatefulWidget {
  const VerificationScreen({super.key});

  @override
  State<VerificationScreen> createState() => _VerificationScreenState();
}

class _VerificationScreenState extends State<VerificationScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController controller = TextEditingController();
  late Timer _timer;
  int _start = 60; // Starting value of the countdown in seconds

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_start > 0) {
        setState(() {
          _start--;
        });
      } else {
        _timer.cancel(); // Stop the timer when countdown reaches 0
      }
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardDismisser(
      child: Scaffold(
        appBar: AppBar(),
        body: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Form(
              key: _formKey,
              child: Padding(
                padding: EdgeInsets.fromLTRB(20.0, 20, 20, 20 + context.padding.bottom),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(),
                    Text(LocaleKeys.enterCode.tr(), style: context.textTheme.displayLarge),
                    const Gap(12),
                    Text(
                      "Отправили код подтверждения на номер\n+998${state.login}",
                      textAlign: TextAlign.center,
                      style: context.textTheme.bodySmall!.copyWith(color: AppColors.darkGrey),
                    ),
                    const Gap(40),
                    Pinput(
                      validator: (value) {
                        if (value!.isEmpty || value.length < 5) {
                          return LocaleKeys.validation.tr();
                        }
                        return null;
                      },
                      controller: controller,
                      length: 5,
                      onChanged: (value) {
                        context.read<AuthBloc>().add(EnterCodeEvent(value));
                      },
                      defaultPinTheme: PinTheme(
                        width: 60,
                        height: 60,
                        textStyle: context.textTheme.labelLarge,
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: AppColors.fillColor),
                      ),
                      focusedPinTheme: PinTheme(
                        width: 60,
                        height: 60,
                        textStyle: context.textTheme.labelLarge,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: AppColors.fillColor,
                          border: Border.all(color: AppColors.primary),
                        ),
                      ),
                    ),
                    const Spacer(flex: 5),
                    if (_start == 0) ...[
                      WButton(
                        isLoading: state.loginStatus.isInProgress,
                        hasBorder: true,
                        onTap: () {
                          context.read<AuthBloc>().add(
                            SubmitLoginEvent(
                              onError: (p0) {},
                              onSuccess: () {
                                _start = 60;
                                startTimer();
                                setState(() {});
                              },
                            ),
                          );
                        },
                        txt: LocaleKeys.resendCode.tr(),
                      ),
                      const Gap(16),
                    ] else ...[
                      Text(
                        LocaleKeys.resendCodeTime.tr(namedArgs: {"time": _start.formatTime()}),
                        style: context.textTheme.bodySmall!.copyWith(color: AppColors.darkGrey),
                      ),
                      const Gap(16),
                    ],
                    WButton(
                      isLoading: state.verifyAuthStatus.isInProgress,
                      onTap: () {
                        if (_formKey.currentState!.validate()) {
                          context.read<AuthBloc>().add(
                            SubmitVerificationEvent(
                              onError: (error) {
                                showError(context, error);
                              },
                              onSuccess: (isFilled) {
                                if (!isFilled) {
                                  context.push(AppRouter.signin);
                                } else {
                                  final isFirstTime = StorageRepository.getBool(StoreKeys.isFirstTime);
                                  if (isFirstTime) {
                                    context.push(AppRouter.setPincode);
                                  } else {}
                                }
                              },
                            ),
                          );
                        }
                      },
                      txt: LocaleKeys.confirm.tr(),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
