import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gap/gap.dart';

class WAddressDropdown extends StatefulWidget {
  const WAddressDropdown({super.key});

  @override
  State<WAddressDropdown> createState() => _WAddressDropdownState();
}

class _WAddressDropdownState extends State<WAddressDropdown> {
  final List<String> items = [
    'Toshkent',
    'Andijon',
    'Buxoro',
    'Namangan',
    "Boshqa"
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return DropdownButtonHideUnderline(
          child: DropdownButton2<String>(
            isExpanded: true,
            hint: Row(
              children: [
                Icon(
                  Icons.location_city,
                  color: state.city.isEmpty ? null : AppColors.primary,
                ),
                const Gap(10),
                Text(
                  LocaleKeys.selectRegion.tr(),
                  style: context.textTheme.headlineMedium!
                      .copyWith(color: Colors.grey.shade800),
                ),
              ],
            ),
            items: items
                .map(
                  (String item) => DropdownMenuItem<String>(
                    value: item,
                    child: Row(
                      children: [
                        Icon(
                          Icons.location_city,
                          color: state.city.isEmpty ? null : AppColors.primary,
                        ),
                        const Gap(10),
                        Text(
                          item,
                          style: context.textTheme.bodyLarge,
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
            value: state.city.isEmpty ? null : state.city,
            onChanged: (String? value) {
              context.read<AuthBloc>().add(SelectCityEvent(value!));
            },
            iconStyleData:
                const IconStyleData(icon: Icon(CupertinoIcons.chevron_down)),
            dropdownStyleData: DropdownStyleData(
                offset: const Offset(0, -15),
                elevation: 0,
                decoration: BoxDecoration(
                    color: AppColors.fillColor,
                    borderRadius: BorderRadius.circular(16))),
            buttonStyleData: ButtonStyleData(
                decoration: BoxDecoration(
                    border: state.city.isEmpty
                        ? null
                        : Border.all(color: AppColors.primary),
                    borderRadius: BorderRadius.circular(20),
                    color: AppColors.fillColor),
                elevation: 0,
                padding: const EdgeInsets.all(16),
                height: 56,
                width: double.maxFinite),
            menuItemStyleData: const MenuItemStyleData(
              height: 40,
            ),
          ),
        );
      },
    );
  }
}
