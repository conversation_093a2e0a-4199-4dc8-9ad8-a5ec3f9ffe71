import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/core/utils/size_config.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_snackbar.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PincodeScreen extends StatefulWidget {
  const PincodeScreen({super.key});

  @override
  State<PincodeScreen> createState() => _PincodeScreenState();
}

class _PincodeScreenState extends State<PincodeScreen> {
  final List<String> _pin = [];
  final int _pinLength = 4;

  void _onKeyboardTap(String value) {
    setState(() {
      if (_pin.length < _pinLength) {
        _pin.add(value);
      }
      if (_pin.length == _pinLength) {}
    });
  }

  void _onDeleteTap() {
    setState(() {
      if (_pin.isNotEmpty) {
        _pin.removeLast();
      }
    });
  }

  Widget _buildPinRow() {
    List<Widget> pinWidgets = [];
    for (int i = 0; i < _pinLength; i++) {
      pinWidgets.add(_buildPinCircle(i < _pin.length ? '●' : null));
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: pinWidgets,
    );
  }

  Widget _buildPinCircle(String? pin) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        border: Border.all(width: 1.5, color: AppColors.primary),
        borderRadius: BorderRadius.circular(12),
      ),
      alignment: Alignment.center,
      child: Text(pin ?? '', style: Theme.of(context).textTheme.displayLarge),
    );
  }

  Widget _buildCustomKeyboard() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildKeyboardRow(['1', '2', '3']),
        _buildKeyboardRow(['4', '5', '6']),
        _buildKeyboardRow(['7', '8', '9']),
        _buildKeyboardRow(['.', '0', 'delete']),
      ],
    );
  }

  Widget _buildKeyboardRow(List<String> values) {
    List<Widget> rowWidgets = [];
    for (String value in values) {
      if (value == 'delete') {
        rowWidgets.add(_buildKeyboardButton(value, _onDeleteTap));
      } else {
        rowWidgets.add(
          _buildKeyboardButton(value, () => _onKeyboardTap(value)),
        );
      }
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: rowWidgets,
    );
  }

  Widget _buildKeyboardButton(String value, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: wi(60),
        height: he(60),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColors.fillColor,
        ),
        margin: const EdgeInsets.only(bottom: 20),
        alignment: Alignment.center,
        child:
            value == 'delete'
                ? const Icon(Icons.backspace_outlined)
                : Text(
                  value,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w500,
                  ),
                ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          TextButton(
            onPressed: () async {
              context.read<AuthBloc>().add(const LogoutEvent());
              context.go(AppRouter.auth);
            },
            child: Text(
              LocaleKeys.logout.tr(),
              style: context.textTheme.bodyLarge!.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
          const Gap(5),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              Text(
                LocaleKeys.helloEnterPin.tr(),
                textAlign: TextAlign.center,
                style: context.textTheme.displayMedium,
              ),
              const Gap(40),
              _buildPinRow(),
              const Spacer(),
              _buildCustomKeyboard(),
              const Gap(20),
              WButton(
                onTap: () async {
                  // print("object");
                  final pin = StorageRepository.getString(StoreKeys.pincode);
                  // print(pin);
                  if (_pin.length == 4) {
                    if (pin == _pin.join()) {
                      context.read<ProfileBloc>().add(GetMeEvent());
                      context.go(AppRouter.navigator);
                    } else {
                      showError(context, "Parol noto'gri");
                    }
                  }
                },
                txt: LocaleKeys.confirm.tr(),
              ),
              Gap(context.padding.bottom),
            ],
          ),
        ),
      ),
    );
  }
}
