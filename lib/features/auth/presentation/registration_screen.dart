import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/auth/presentation/widgets/w_address_dropdown.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_text_field.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:go_router/go_router.dart';

class RegistrationScreen extends StatefulWidget {
  const RegistrationScreen({super.key});

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController birhtDateController = TextEditingController();
  var maskFormatter = MaskTextInputFormatter(
      mask: '##.##.####',
      filter: {"#": RegExp(r'[0-9]')},
      type: MaskAutoCompletionType.lazy);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          return Form(
            key: _formKey,
            child: Center(
              child: Padding(
                padding: EdgeInsets.fromLTRB(
                    20.0, 20, 20, 20 + context.padding.bottom),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(),
                    Text(
                      LocaleKeys.createAccount.tr(),
                      style: context.textTheme.displayLarge,
                    ),
                    const Gap(40),
                    WTextField(
                      validator: (value) {
                        if (value!.isEmpty) {
                          return LocaleKeys.validation.tr();
                        }
                        return null;
                      },
                      onChanged: (value) => context
                          .read<AuthBloc>()
                          .add(EnterFullNameEvent(value)),
                      capitalization: TextCapitalization.words,
                      controller: fullNameController,
                      isEmpty: state.fullName.isEmpty,
                      prefixIcon: Icons.person,
                      labelText: LocaleKeys.fio.tr(),
                    ),
                    const Gap(16),
                    WTextField(
                      validator: (value) {
                        if (value!.isEmpty) {
                          return LocaleKeys.validation.tr();
                        }
                        return null;
                      },
                      keyboardType: TextInputType.datetime,
                      onChanged: (value) => context
                          .read<AuthBloc>()
                          .add(EnterBirthDateEvent(value)),
                      maskFormatter: [maskFormatter],
                      capitalization: TextCapitalization.words,
                      controller: birhtDateController,
                      isEmpty: state.birthDate.isEmpty,
                      prefixIcon: Icons.date_range,
                      labelText: "${LocaleKeys.birthDate.tr()} (dd.mm.yyyy)",
                    ),
                    const Gap(16),
                    const WAddressDropdown(),
                    const Spacer(
                      flex: 5,
                    ),
                    WButton(
                        isLoading: state.loginStatus.isInProgress,
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            context.push(AppRouter.takePicture);
                          }
                        },
                        txt: LocaleKeys.singin.tr())
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
