part of 'auth_bloc.dart';

sealed class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class EnterLoginEvent extends AuthEvent {
  final String value;
  const EnterLoginEvent(this.value);
}

class EnterCodeEvent extends AuthEvent {
  final String value;
  const EnterCodeEvent(this.value);
}

class SubmitLoginEvent extends AuthEvent {
  final Function(String) onError;
  final Function() onSuccess;

  const SubmitLoginEvent({required this.onError, required this.onSuccess});
}

class SubmitVerificationEvent extends AuthEvent {
  final Function(String) onError;
  final Function(bool) onSuccess;

  const SubmitVerificationEvent({
    required this.onError,
    required this.onSuccess,
  });
}

class EnterFullNameEvent extends AuthEvent {
  final String value;
  const EnterFullNameEvent(this.value);
}

class EnterBirthDateEvent extends AuthEvent {
  final String value;
  const EnterBirthDateEvent(this.value);
}

class SelectCityEvent extends AuthEvent {
  final String value;
  const SelectCityEvent(this.value);
}

class LogoutEvent extends AuthEvent {
  const LogoutEvent();
}

class CheckStatusEvent extends AuthEvent {
  const CheckStatusEvent();
}

class SubmitRegisterEvent extends AuthEvent {
  final XFile file;
  final Function(String) onError;
  final Function() onSuccess;
  const SubmitRegisterEvent({
    required this.file,
    required this.onError,
    required this.onSuccess,
  });
}

class GetTeamsEvent extends AuthEvent {}

class SelectTeamEvent extends AuthEvent {
  final ItemEntity item;
  const SelectTeamEvent(this.item);
}

class UpdateTeamEvent extends AuthEvent {
  final Function(String) onError;
  final Function() onSuccess;

  const UpdateTeamEvent({required this.onError, required this.onSuccess});
}

class DeleteUserEvent extends AuthEvent {
  final Function(String) onError;
  final Function() onSuccess;

  const DeleteUserEvent({required this.onError, required this.onSuccess});
}
