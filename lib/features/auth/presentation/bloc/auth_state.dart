// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'auth_bloc.dart';

class AuthState extends Equatable {
  final String login;
  final String code;
  final FormzSubmissionStatus loginStatus;
  final FormzSubmissionStatus verifyAuthStatus;
  final String city;
  final String fullName;
  final String birthDate;
  final AuthenticationStatus authStatus;
  final FormzSubmissionStatus registerStatus;
  final ItemEntity selectedTeam;
  final List<ItemEntity> teams;
  final FormzSubmissionStatus teamsStatus;
  final FormzSubmissionStatus updateTeamStatus;
  final FormzSubmissionStatus deleteUserStatus;
  const AuthState({
    this.login = "",
    this.code = "",
    this.loginStatus = FormzSubmissionStatus.initial,
    this.verifyAuthStatus = FormzSubmissionStatus.initial,
    this.birthDate = "",
    this.city = "",
    this.authStatus = AuthenticationStatus.unauthenticated,
    this.fullName = "",
    this.registerStatus = FormzSubmissionStatus.initial,
    this.selectedTeam = const ItemEntity(),
    this.teams = const [],
    this.teamsStatus = FormzSubmissionStatus.initial,
    this.updateTeamStatus = FormzSubmissionStatus.initial,
    this.deleteUserStatus = FormzSubmissionStatus.initial,
  });

  @override
  List<Object> get props => [
    login,
    code,
    loginStatus,
    verifyAuthStatus,
    birthDate,
    city,
    fullName,
    authStatus,
    registerStatus,
    selectedTeam,
    teams,
    teamsStatus,
    updateTeamStatus,
    deleteUserStatus,
  ];

  AuthState copyWith({
    String? login,
    String? code,
    FormzSubmissionStatus? loginStatus,
    FormzSubmissionStatus? verifyAuthStatus,
    String? city,
    String? fullName,
    String? birthDate,
    AuthenticationStatus? authStatus,
    FormzSubmissionStatus? registerStatus,
    ItemEntity? selectedTeam,
    List<ItemEntity>? teams,
    FormzSubmissionStatus? teamsStatus,
    FormzSubmissionStatus? updateTeamStatus,
    FormzSubmissionStatus? deleteUserStatus,
  }) {
    return AuthState(
      login: login ?? this.login,
      code: code ?? this.code,
      loginStatus: loginStatus ?? this.loginStatus,
      verifyAuthStatus: verifyAuthStatus ?? this.verifyAuthStatus,
      city: city ?? this.city,
      fullName: fullName ?? this.fullName,
      birthDate: birthDate ?? this.birthDate,
      authStatus: authStatus ?? this.authStatus,
      registerStatus: registerStatus ?? this.registerStatus,
      selectedTeam: selectedTeam ?? this.selectedTeam,
      teams: teams ?? this.teams,
      teamsStatus: teamsStatus ?? this.teamsStatus,
      updateTeamStatus: updateTeamStatus ?? this.updateTeamStatus,
      deleteUserStatus: deleteUserStatus ?? this.deleteUserStatus,
    );
  }
}

final class AuthInitial extends AuthState {}
