import 'package:bloc/bloc.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/auth/domain/usecases/auth_use_case.dart';
import 'package:echipta/features/auth/domain/usecases/register_use_case.dart';
import 'package:echipta/features/auth/domain/usecases/teams_use_case.dart';
import 'package:echipta/features/auth/domain/usecases/update_team_use_case.dart';
import 'package:echipta/features/auth/domain/usecases/verify_auth_use_case.dart';
import 'package:echipta/features/profile/domain/usecases/delete_user_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';
import 'package:image_picker/image_picker.dart';

part 'auth_event.dart';
part 'auth_state.dart';

enum AuthenticationStatus { authenticated, unauthenticated }

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthUseCase _authUseCase = AuthUseCase();
  final VerifyAuthUseCase _verifyAuthUseCase = VerifyAuthUseCase();
  final RegisterUseCase _registerUseCase = RegisterUseCase();
  final TeamsUseCase _teamsUseCase = TeamsUseCase();
  final UpdateTeamUseCase _updateTeamUseCase = UpdateTeamUseCase();
  final DeleteUserUseCase _deleteUserUseCase = DeleteUserUseCase();
  AuthBloc() : super(AuthInitial()) {
    on<EnterLoginEvent>(
      (event, emit) => emit(state.copyWith(login: event.value)),
    );
    on<EnterCodeEvent>(
      (event, emit) => emit(state.copyWith(code: event.value)),
    );
    on<SubmitLoginEvent>((event, emit) async {
      emit(state.copyWith(loginStatus: FormzSubmissionStatus.inProgress));
      final params = AuthParams(phoneNumber: state.login);
      final result = await _authUseCase.call(params);
      if (result.isRight) {
        event.onSuccess();
        emit(state.copyWith(loginStatus: FormzSubmissionStatus.success));
      } else {
        event.onError(result.left.errorMessage ?? "Error");
        emit(state.copyWith(loginStatus: FormzSubmissionStatus.failure));
      }
    });
    on<SubmitVerificationEvent>((event, emit) async {
      emit(state.copyWith(verifyAuthStatus: FormzSubmissionStatus.inProgress));
      final params = AuthParams(phoneNumber: state.login, code: state.code);
      final result = await _verifyAuthUseCase.call(params);
      if (result.isRight) {
        emit(state.copyWith(verifyAuthStatus: FormzSubmissionStatus.success));
        event.onSuccess(result.right);
      } else {
        event.onError(result.left.errorMessage ?? "Error");
        emit(state.copyWith(verifyAuthStatus: FormzSubmissionStatus.failure));
      }
    });
    on<EnterFullNameEvent>(
      (event, emit) => emit(state.copyWith(fullName: event.value)),
    );
    on<EnterBirthDateEvent>(
      (event, emit) => emit(state.copyWith(birthDate: event.value)),
    );
    on<SelectCityEvent>(
      (event, emit) => emit(state.copyWith(city: event.value)),
    );
    on<LogoutEvent>((event, emit) {
      StorageRepository.deleteString(StoreKeys.token);
      StorageRepository.deleteString(StoreKeys.pincode);
      StorageRepository.deleteString(StoreKeys.passToken);
      emit(state.copyWith(authStatus: AuthenticationStatus.unauthenticated));
    });

    on<CheckStatusEvent>((event, emit) async {
      final token = StorageRepository.getString(StoreKeys.token);
      if (token.isNotEmpty) {
        emit(state.copyWith(authStatus: AuthenticationStatus.authenticated));
      } else {
        emit(state.copyWith(authStatus: AuthenticationStatus.unauthenticated));
      }
    });
    on<SubmitRegisterEvent>((event, emit) async {
      emit(state.copyWith(registerStatus: FormzSubmissionStatus.inProgress));
      final params = UserParams(
        fullName: state.fullName,
        birthDate: state.birthDate,
        avatar: event.file,
      );
      final result = await _registerUseCase.call(params);
      if (result.isRight) {
        event.onSuccess();
        emit(state.copyWith(registerStatus: FormzSubmissionStatus.success));
      } else {
        event.onError(result.left.errorMessage ?? "Error");
        emit(state.copyWith(registerStatus: FormzSubmissionStatus.failure));
      }
    });
    on<GetTeamsEvent>((event, emit) async {
      emit(state.copyWith(teamsStatus: FormzSubmissionStatus.inProgress));
      final result = await _teamsUseCase.call(NoParams());
      if (result.isRight) {
        emit(
          state.copyWith(
            teams: result.right,
            teamsStatus: FormzSubmissionStatus.success,
          ),
        );
      } else {
        emit(state.copyWith(teamsStatus: FormzSubmissionStatus.failure));
      }
    });
    on<SelectTeamEvent>(
      (event, emit) => emit(state.copyWith(selectedTeam: event.item)),
    );
    on<UpdateTeamEvent>((event, emit) async {
      emit(state.copyWith(updateTeamStatus: FormzSubmissionStatus.inProgress));
      final params = IdParam(id: state.selectedTeam.id);
      final result = await _updateTeamUseCase.call(params);
      if (result.isRight) {
        event.onSuccess();
        emit(state.copyWith(updateTeamStatus: FormzSubmissionStatus.success));
      } else {
        event.onError(result.left.errorMessage ?? "Error");
        emit(state.copyWith(updateTeamStatus: FormzSubmissionStatus.failure));
      }
    });
    on<DeleteUserEvent>((event, emit) async {
      emit(state.copyWith(deleteUserStatus: FormzSubmissionStatus.inProgress));
      final result = await _deleteUserUseCase.call(NoParams());
      if (result.isRight) {
        event.onSuccess();
        emit(state.copyWith(deleteUserStatus: FormzSubmissionStatus.success));
      } else {
        event.onError(result.left.errorMessage ?? "Error");
        emit(state.copyWith(deleteUserStatus: FormzSubmissionStatus.failure));
      }
    });
  }
}
