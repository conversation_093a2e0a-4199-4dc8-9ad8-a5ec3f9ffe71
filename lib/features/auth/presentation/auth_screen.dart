import 'dart:io';

import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_snackbar.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  var maskFormatter = MaskTextInputFormatter(
      mask: ' (##) ### ## ##',
      filter: {"#": RegExp(r'[0-9]')},
      type: MaskAutoCompletionType.lazy);
  final TextEditingController controller = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return KeyboardDismisser(
      child: Scaffold(
        appBar: AppBar(
          leading: IconButton(
              onPressed: () {
                SystemNavigator.pop();
              },
              icon: Icon(Platform.isAndroid
                  ? Icons.arrow_back
                  : Icons.arrow_back_ios_new)),
        ),
        body: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Form(
              key: _formKey,
              child: Center(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                      20.0, 20, 20, 20 + context.padding.bottom),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Spacer(),
                      SvgPicture.asset(AppAssets.blueLogoFull),
                      const Spacer(),
                      Text(
                        LocaleKeys.login.tr(),
                        style: context.textTheme.displayLarge,
                      ),
                      const Gap(40),
                      TextFormField(
                        keyboardType: TextInputType.phone,
                        controller: controller,
                        style: context.textTheme.bodyLarge,
                        onChanged: (value) {
                          context.read<AuthBloc>().add(EnterLoginEvent(value));
                        },
                        validator: (value) {
                          if (value!.isEmpty || value.length < 15) {
                            return LocaleKeys.validation.tr();
                          }
                          return null;
                        },
                        inputFormatters: [maskFormatter],
                        decoration: InputDecoration(
                          prefixText: "+998",
                          labelText: LocaleKeys.phoneNumber.tr(),
                          prefixIcon: Icon(
                            Icons.phone,
                            color: state.login.isEmpty
                                ? AppColors.black
                                : AppColors.primary,
                          ),
                          contentPadding: const EdgeInsets.all(16),
                          filled: true,
                          prefixStyle: context.textTheme.bodyLarge,
                          fillColor: AppColors.fillColor,
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: state.login.isEmpty
                                ? BorderSide.none
                                : const BorderSide(color: AppColors.primary),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: const BorderSide(color: AppColors.red),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: const BorderSide(color: AppColors.red),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: const BorderSide(
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                      ),
                      const Spacer(
                        flex: 5,
                      ),
                      WButton(
                          isLoading: state.loginStatus.isInProgress,
                          onTap: () {
                            if (_formKey.currentState!.validate()) {
                              FocusScope.of(context).unfocus();
                              context.read<AuthBloc>().add(
                                    SubmitLoginEvent(
                                      onError: (error) {
                                        showError(context, error);
                                      },
                                      onSuccess: () {
                                        context.push(AppRouter.verification);
                                      },
                                    ),
                                  );
                            }
                          },
                          txt: LocaleKeys.sendCode.tr())
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
