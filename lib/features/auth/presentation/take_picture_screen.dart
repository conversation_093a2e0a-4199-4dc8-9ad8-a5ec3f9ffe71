import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';

class TakePictureScreen extends StatefulWidget {
  const TakePictureScreen({super.key});

  @override
  State<TakePictureScreen> createState() => _TakePictureScreenState();
}

class _TakePictureScreenState extends State<TakePictureScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.all(20.0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(AppAssets.face, width: MediaQuery.of(context).size.width),
                  const Gap(20),
                  Text("Yuzingizni suratga oling", style: context.textTheme.displaySmall),
                  const Gap(10),
                  Text(
                    "Yuzingiz kamera yaqinroq hamda yorig'roq joyda rasmga oling!",
                    textAlign: TextAlign.center,
                    style: context.textTheme.bodySmall!.copyWith(color: AppColors.darkGrey),
                  ),
                  const Gap(16),
                  WButton(
                    isLoading: state.registerStatus.isInProgress,
                    onTap: () async {
                      final ImagePicker picker = ImagePicker();
                      final XFile? image = await picker
                          .pickImage(
                            source: ImageSource.camera,
                            imageQuality: 50,
                            preferredCameraDevice: CameraDevice.front,
                          )
                          .then((value) {
                            if (value == null) return;
                            final passToken = StorageRepository.getString(StoreKeys.passToken);
                            context.read<AuthBloc>().add(
                              SubmitRegisterEvent(
                                file: value,
                                onError: (p0) {
                                  showError(context, p0);
                                },
                                onSuccess: () async {
                                  await StorageRepository.putString(StoreKeys.token, passToken);
                                  context.push(AppRouter.team);
                                },
                              ),
                            );
                          });

                      if (image == null) return;
                    },
                    txt: "Rasmga olish",
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
