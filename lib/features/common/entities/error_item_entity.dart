
import 'package:echipta/features/common/models/error_item_model.dart';
import 'package:json_annotation/json_annotation.dart';

class ErrorItemEntity {
  final String error;
  final String field;
  final String message;

  const ErrorItemEntity({
    this.field = '',
    this.message = '',
    this.error = '',
  });
}

class ErrorItemConverter implements JsonConverter<ErrorItemEntity, Map<String, dynamic>?> {
  const ErrorItemConverter();

  @override
  ErrorItemEntity fromJson(Map<String, dynamic>? json) => ErrorItemModel.fromJson(json ?? {});

  @override
  Map<String, dynamic>? toJson(ErrorItemEntity object) => {};
}
