
import 'package:echipta/features/common/entities/error_item_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'error_item_model.g.dart';

@JsonSerializable(createToJson: false)
class ErrorItemModel extends ErrorItemEntity {
  const ErrorItemModel({required super.error, required super.message, required super.field});

  factory ErrorItemModel.fromJson(Map<String, dynamic> json) => _$ErrorItemModelFromJson(json);
}
