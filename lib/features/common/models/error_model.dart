
import 'package:echipta/features/common/entities/error_entity.dart';
import 'package:echipta/features/common/entities/error_item_entity.dart';
import 'package:json_annotation/json_annotation.dart';

part 'error_model.g.dart';

@JsonSerializable(createToJson: false)
class ErrorModel extends ErrorEntity {
  const ErrorModel({
    required super.errors,
    required super.statusCode,
  });

  factory ErrorModel.fromJson(Map<String, dynamic> json) => _$ErrorModelFromJson(json);
}
