import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class WTextField extends StatelessWidget {
  const WTextField({
    super.key,
    required this.controller,
    required this.isEmpty,
    this.maskFormatter,
    this.onChanged,
    required this.labelText,
    required this.prefixIcon,
    this.capitalization,
    this.enabled,
    this.onTap,
    this.prefixText,
    this.keyboardType,
    this.validator,
  });
  final TextEditingController controller;
  final ValueChanged? onChanged;
  final List<TextInputFormatter>? maskFormatter;
  final bool isEmpty;
  final IconData prefixIcon;
  final String labelText;
  final String? prefixText;
  final TextCapitalization? capitalization;
  final VoidCallback? onTap;
  final bool? enabled;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onTap: onTap,
      keyboardType: keyboardType,
      enabled: enabled,
      textCapitalization: capitalization ?? TextCapitalization.none,
      controller: controller,
      style: context.textTheme.bodyLarge,
      onChanged: onChanged,
      validator: validator,
      inputFormatters: maskFormatter,
      decoration: InputDecoration(
        prefixText: prefixText,
        labelText: labelText,
        prefixIcon: Icon(
          prefixIcon,
          color: isEmpty ? AppColors.black : AppColors.primary,
        ),
        contentPadding: const EdgeInsets.all(16),
        filled: true,
        prefixStyle: context.textTheme.bodyLarge,
        fillColor: AppColors.fillColor,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: isEmpty
              ? BorderSide.none
              : const BorderSide(color: AppColors.primary),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: const BorderSide(color: AppColors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: const BorderSide(color: AppColors.red),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: const BorderSide(
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }
}
