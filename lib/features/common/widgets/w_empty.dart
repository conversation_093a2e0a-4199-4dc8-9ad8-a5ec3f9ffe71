import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class WEmptyScreen extends StatelessWidget {
  const WEmptyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.5,
            child: SvgPicture.asset(
              AppAssets.empty,
              fit: BoxFit.cover,
              height: MediaQuery.of(context).size.height * 0.5,
            ),
          ),
          Text("Bu yer hozircha bo'sh", style: context.textTheme.bodyLarge),
        ],
      ),
    );
  }
}
