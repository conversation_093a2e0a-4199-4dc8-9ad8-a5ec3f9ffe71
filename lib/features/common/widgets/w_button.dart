import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:flutter/material.dart';

class WButton extends StatelessWidget {
  const WButton({
    super.key,
    this.hasBorder = false,
    this.btnColor,
    this.txtColor,
    required this.onTap,
    required this.txt,
    this.isLoading = false,
  });
  final bool hasBorder;
  final Color? btnColor;
  final Color? txtColor;
  final String txt;
  final VoidCallback onTap;
  final bool isLoading;
  @override
  Widget build(BuildContext context) {
    if (hasBorder) {
      return InkWell(
        borderRadius: BorderRadius.circular(200),
        onTap: onTap,
        child: Ink(
          height: 56,
          width: double.maxFinite,
          decoration: BoxDecoration(
            border: Border.all(color: btnColor ?? AppColors.primary),
            color: AppColors.white,
            borderRadius: BorderRadius.circular(200),
          ),
          child: Center(
            child:
                isLoading
                    ? const CircularProgressIndicator.adaptive(backgroundColor: AppColors.primary)
                    : Text(txt, style: context.textTheme.labelLarge!.copyWith(color: txtColor ?? AppColors.primary)),
          ),
        ),
      );
    } else {
      return InkWell(
        borderRadius: BorderRadius.circular(200),
        onTap: onTap,
        child: Ink(
          height: 56,
          width: double.maxFinite,
          decoration: BoxDecoration(color: btnColor ?? AppColors.primary, borderRadius: BorderRadius.circular(200)),
          child: Center(
            child:
                isLoading
                    ? const CircularProgressIndicator.adaptive(backgroundColor: AppColors.white)
                    : Text(txt, style: context.textTheme.labelLarge!.copyWith(color: txtColor ?? AppColors.white)),
          ),
        ),
      );
    }
  }
}
