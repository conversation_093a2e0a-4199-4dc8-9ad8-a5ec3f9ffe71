import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/ticket/data/repository/ticket_repository_impl.dart';
import 'package:echipta/features/ticket/domain/repository/ticket_repository.dart';

class TicketUseCase extends UseCase<dynamic, NoParams> {
  final TicketRepository _repository = serviceLocator<TicketRepositoryImpl>();
  @override
  Future<Either<Failure, dynamic>> call(NoParams params) async {
    return await _repository.getCurrentTicket();
  }
}
