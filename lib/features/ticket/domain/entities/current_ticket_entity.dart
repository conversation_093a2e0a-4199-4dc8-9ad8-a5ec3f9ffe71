// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';

import 'package:echipta/features/home/<USER>/entities/match_entity.dart';

class CurrentTicketEntity extends Equatable {
  final String qr_code;
  final int id;
  final String sector;
  final int row;
  final int seat;
  @MatchConverter()
  final MatchEntity match;

  const CurrentTicketEntity({
    this.qr_code = "",
    this.id = 0,
    this.sector = "",
    this.row = 0,
    this.seat = 0,
    this.match = const MatchEntity(),
  });
  @override
  List<Object?> get props => [
        qr_code,
        id,
        sector,
        row,
        seat,
        match,
      ];

  CurrentTicketEntity copyWith({
    String? qr_code,
    int? id,
    String? sector,
    int? row,
    int? seat,
    MatchEntity? match,
  }) {
    return CurrentTicketEntity(
      qr_code: qr_code ?? this.qr_code,
      id: id ?? this.id,
      sector: sector ?? this.sector,
      row: row ?? this.row,
      seat: seat ?? this.seat,
      match: match ?? this.match,
    );
  }
}
