import 'dart:async';

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ticket/presentation/bloc/ticket_bloc.dart';
import 'package:echipta/features/ticket/presentation/widgets/w_current_ticket_body.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:echipta/features/navigation/presentation/navigation_screen.dart';

class CurrentTicketScreen extends StatefulWidget {
  const CurrentTicketScreen({super.key});

  @override
  State<CurrentTicketScreen> createState() => _CurrentTicketScreenState();
}

class _CurrentTicketScreenState extends State<CurrentTicketScreen> {
  Timer? timer;
  Timer? loadingtimer;
  ValueNotifier<int> loading = ValueNotifier(15000);
  PageController? _pageController;
  bool _isTicketTabActive = false;

  @override
  void initState() {
    super.initState();
    print("🎫 Current Ticket Screen: Initializing");

    // Get the page controller from the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        _pageController = HomeTabControllerProvider.of(context).controller;
        _pageController?.addListener(_onPageChanged);
        _checkIfTicketTabActive();
      } catch (e) {
        print("❌ Could not access page controller: $e");
      }
    });

    context.read<TicketBloc>().add(
      GetCurrentTicketEvent(
        whenDone: () {
          if (mounted) {
            loadingtimer?.cancel();
            startLoadingTimer();
          }
        },
      ),
    );
    _startApiCall();
  }

  void _onPageChanged() {
    _checkIfTicketTabActive();
  }

  void _checkIfTicketTabActive() {
    if (_pageController != null) {
      final currentPage = _pageController!.page?.round() ?? 0;
      final isTicketActive = currentPage == 2; // Ticket tab is index 2

      if (_isTicketTabActive != isTicketActive) {
        _isTicketTabActive = isTicketActive;
        print("🎫 Ticket tab active: $_isTicketTabActive");

        if (_isTicketTabActive) {
          // When returning to ticket tab, make immediate API call and start timer
          print("🔄 Returning to ticket tab - making immediate API call");
          context.read<TicketBloc>().add(
            GetCurrentTicketEvent(
              whenDone: () {
                if (mounted) {
                  loadingtimer?.cancel();
                  startLoadingTimer();
                }
              },
            ),
          );
          _startApiCall();
        } else {
          _stopApiCall();
        }
      }
    }
  }

  void _startApiCall() {
    // Cancel any existing timer first
    timer?.cancel();

    print("▶️ Starting API timer for current ticket");
    timer = Timer.periodic(Duration(seconds: 15), (timer) {
      // Check if the widget is still mounted and tab is still active
      if (mounted && _isTicketTabActive) {
        print("🔄 Current Ticket Timer: Making API call");
        context.read<TicketBloc>().add(
          GetCurrentTicketEvent(
            whenDone: () {
              if (mounted) {
                loadingtimer?.cancel();
                startLoadingTimer();
              }
            },
          ),
        );
      } else {
        print("❌ Current Ticket Timer: Stopping - widget not mounted or tab not active");
        timer.cancel();
      }
    });
  }

  void _stopApiCall() {
    print("⏹️ Stopping API timer for current ticket");
    timer?.cancel();
    timer = null;
  }

  void startLoadingTimer() {
    loading.value = 15000;
    loadingtimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (loading.value == 0) {
        loadingtimer?.cancel();
      } else {
        loading.value -= 50;
      }
    });
  }

  @override
  void dispose() {
    print("🗑️ Current Ticket Screen: Disposing and canceling timers");

    // Remove page controller listener
    _pageController?.removeListener(_onPageChanged);

    // Cancel timers with explicit null check and logging
    if (timer != null) {
      timer!.cancel();
      timer = null;
      print("✅ Main API timer canceled");
    }

    if (loadingtimer != null) {
      loadingtimer!.cancel();
      loadingtimer = null;
      print("✅ Loading timer canceled");
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(bottom: Radius.circular(12))),
        backgroundColor: AppColors.primary,
        title: Text(
          LocaleKeys.currentTicket.tr(),
          style: context.textTheme.displaySmall!.copyWith(color: AppColors.white),
        ),
      ),
      body: Column(
        children: [
          Gap(20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: ValueListenableBuilder(
              valueListenable: loading,
              builder: (context, value, child) {
                return LinearPercentIndicator(
                  width: MediaQuery.of(context).size.width,
                  lineHeight: 7,
                  barRadius: const Radius.circular(20),
                  percent: loading.value / 15000,
                  backgroundColor: Colors.grey,
                  progressColor: AppColors.primary,
                );
              },
            ),
          ),
          Expanded(child: WCurrentTicketBody()),
        ],
      ),
    );
  }
}
