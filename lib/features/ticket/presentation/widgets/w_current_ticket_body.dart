import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ticket/domain/entities/current_ticket_entity.dart';
import 'package:echipta/features/ticket/presentation/bloc/ticket_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';

class WCurrentTicketBody extends StatelessWidget {
  const WCurrentTicketBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      width: double.maxFinite,
      margin: EdgeInsets.fromLTRB(20, 20, 20, 20 + context.padding.bottom),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: const [BoxShadow(offset: Offset(10, 10), spreadRadius: 0, blurRadius: 0, color: AppColors.primary)],
        border: Border.all(color: AppColors.primary),
        borderRadius: BorderRadius.circular(16),
      ),
      child: BlocBuilder<TicketBloc, TicketState>(
        builder: (context, state) {
          if (state.currentTicketStatus.isInProgress) {
            return const Center(child: CircularProgressIndicator.adaptive());
          } else if (state.currentTicket == const CurrentTicketEntity()) {
            return Center(child: Text(LocaleKeys.noCurrentTicket.tr()));
          } else if (state.currentTicketStatus.isSuccess) {
            final item = state.currentTicket;
            final decodedBytes = base64Decode(item.qr_code);

            return Column(
              children: [
                Text(item.match.title, style: context.textTheme.displaySmall!.copyWith(color: AppColors.primary)),
                const Gap(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      item.match.main_team.name,
                      style: context.textTheme.bodySmall!.copyWith(
                        color: AppColors.darkGrey,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    CachedNetworkImage(imageUrl: item.match.main_team.image, width: 50, height: 50),
                    const Gap(10),
                    Text(
                      "vs",
                      style: context.textTheme.displayLarge!.copyWith(
                        color: AppColors.darkGrey,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const Gap(10),
                    CachedNetworkImage(imageUrl: item.match.second_team.image, width: 50, height: 50),
                    const Gap(10),
                    Text(
                      item.match.second_team.name,
                      style: context.textTheme.bodySmall!.copyWith(
                        color: AppColors.darkGrey,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                const Gap(20),
                const Divider(height: 0),
                SizedBox(
                  height: 60,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("Sektor", style: context.textTheme.displaySmall!.copyWith(color: AppColors.darkGrey)),
                          Text(item.sector.toUpperCase(), style: context.textTheme.displayLarge),
                        ],
                      ),
                      const Gap(20),
                      const VerticalDivider(),
                      const Gap(20),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("Qator", style: context.textTheme.displaySmall!.copyWith(color: AppColors.darkGrey)),
                          Text(item.row.toString().toUpperCase(), style: context.textTheme.displayLarge),
                        ],
                      ),
                      const Gap(20),
                      const VerticalDivider(),
                      const Gap(20),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("Joy", style: context.textTheme.displaySmall!.copyWith(color: AppColors.darkGrey)),
                          Text(item.seat.toString().toUpperCase(), style: context.textTheme.displayLarge),
                        ],
                      ),
                    ],
                  ),
                ),
                const Divider(height: 0),
                const Expanded(
                  child: Center(
                    child: Text(
                      "50 000 so'm",
                      style: TextStyle(fontSize: 48, fontWeight: FontWeight.w700, color: AppColors.primary),
                    ),
                  ),
                ),
                // const Spacer(),
                Image.memory(decodedBytes),
              ],
            );
          } else {
            return Center(
              child: Text(
                "No Current Ticket",
                style: context.textTheme.displaySmall!.copyWith(color: AppColors.primary),
              ),
            );
          }
        },
      ),
    );
  }
}
