import 'package:bloc/bloc.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/features/ticket/domain/entities/current_ticket_entity.dart';
import 'package:echipta/features/ticket/domain/usecases/ticket_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

part 'ticket_event.dart';
part 'ticket_state.dart';

class TicketBloc extends Bloc<TicketEvent, TicketState> {
  final TicketUseCase _ticketUseCase = TicketUseCase();
  TicketBloc() : super(TicketInitial()) {
    on<GetCurrentTicketEvent>((event, emit) async {
      emit(state.copyWith(
          currentTicketStatus: FormzSubmissionStatus.inProgress));
      final result = await _ticketUseCase.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(
            currentTicket: result.right,
            currentTicketStatus: FormzSubmissionStatus.success));
      } else {
        emit(
            state.copyWith(currentTicketStatus: FormzSubmissionStatus.failure));
      }
      event.whenDone();
    });
  }
}
