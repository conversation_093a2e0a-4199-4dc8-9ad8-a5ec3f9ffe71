// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'ticket_bloc.dart';

class TicketState extends Equatable {
  final CurrentTicketEntity currentTicket;
  final FormzSubmissionStatus currentTicketStatus;
  const TicketState({
    this.currentTicket = const CurrentTicketEntity(),
    this.currentTicketStatus = FormzSubmissionStatus.initial,
  });

  @override
  List<Object> get props => [
        currentTicket,
        currentTicketStatus,
      ];

  TicketState copyWith({
    CurrentTicketEntity? currentTicket,
    FormzSubmissionStatus? currentTicketStatus,
  }) {
    return TicketState(
      currentTicket: currentTicket ?? this.currentTicket,
      currentTicketStatus: currentTicketStatus ?? this.currentTicketStatus,
    );
  }
}

final class TicketInitial extends TicketState {}
