import 'package:dio/dio.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/ticket/data/datasource/ticket_datasource.dart';
import 'package:echipta/features/ticket/domain/repository/ticket_repository.dart';

class TicketRepositoryImpl implements TicketRepository {
  final TicketDatasource _datasource = serviceLocator<TicketDatasourceImpl>();
  @override
  Future<Either<Failure, dynamic>> getCurrentTicket() async {
    try {
      final result = await _datasource.getCurrentTicket();
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey));
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }
}
