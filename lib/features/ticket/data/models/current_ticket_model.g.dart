// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'current_ticket_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CurrentTicketModel _$CurrentTicketModelFromJson(Map<String, dynamic> json) =>
    CurrentTicketModel(
      id: (json['id'] as num?)?.toInt() ?? 0,
      match:
          json['match'] == null
              ? const MatchEntity()
              : const MatchConverter().fromJson(
                json['match'] as Map<String, dynamic>,
              ),
      qr_code: json['qr_code'] as String? ?? "",
      row: (json['row'] as num?)?.toInt() ?? 0,
      seat: (json['seat'] as num?)?.toInt() ?? 0,
      sector: json['sector'] as String? ?? "",
    );

Map<String, dynamic> _$CurrentTicketModelToJson(CurrentTicketModel instance) =>
    <String, dynamic>{
      'qr_code': instance.qr_code,
      'id': instance.id,
      'sector': instance.sector,
      'row': instance.row,
      'seat': instance.seat,
      'match': const MatchConverter().toJson(instance.match),
    };
