import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/ticket/domain/entities/current_ticket_entity.dart';
import 'package:json_annotation/json_annotation.dart';
part 'current_ticket_model.g.dart';
@JsonSerializable(fieldRename: FieldRename.snake)
class CurrentTicketModel extends CurrentTicketEntity {
  const CurrentTicketModel({
    super.id,
    super.match,
    super.qr_code,
    super.row,
    super.seat,
    super.sector,
  });

  factory CurrentTicketModel.fromJson(Map<String, dynamic> json) => _$CurrentTicketModelFromJson(json);
}
