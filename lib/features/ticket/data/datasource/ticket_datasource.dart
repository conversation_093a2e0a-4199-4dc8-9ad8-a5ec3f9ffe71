import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/common/models/error_model.dart';
import 'package:echipta/features/ticket/data/models/current_ticket_model.dart';

abstract class TicketDatasource {
  Future<dynamic> getCurrentTicket();
}

class TicketDatasourceImpl implements TicketDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;
  @override
  Future<dynamic> getCurrentTicket() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);

      final response = await _dio.get(
        "/games/active-ticket/",
        options: Options(headers: {"Authorization": token}),
      );

      print(response.data);
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        if (response.data["data"].runtimeType == List) {
          return null;
        }
        return CurrentTicketModel.fromJson(response.data["data"]["ticket"]);
      } else {
        if (response.data['data'] !=null && response.data is Map<String, dynamic>) {

          final error = ErrorModel.fromJson(response.data);
          if (error.errors.isNotEmpty) {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: error.errors.first.message,
                errorKey: error.errors.first.error);
          } else {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: response.data.toString(),
                errorKey: error.errors.first.error);
          }
        } else {
          throw ServerException(
              statusCode: response.statusCode ?? 500,
              errorMessage: response.data.toString(),
              errorKey: "Unknown error");
        }
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
