// ignore_for_file: deprecated_member_use

import 'package:cached_network_image/cached_network_image.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/gift/presentation/bloc/gift_bloc.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';

class WMatchItemForGift extends StatelessWidget {
  const WMatchItemForGift({
    super.key,
    required this.item,
    required this.index,
  });

  final MatchEntity item;
  final int index;
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GiftBloc, GiftState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            context.read<OrderBloc>().add(SelectMatchEvent(match: item));
            context.read<GiftBloc>().add(SelectMatchForGiftEvent(item));
          },
          child: SizedBox(
            height: 155,
            child: Stack(
              children: [
                Container(
                  height: 155,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: Colors.primaries[index % Colors.primaries.length],
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    children: [
                      Text(
                        // "28.06.2024 | 17:00",
                        DateFormat("dd.MM.yyyy | HH:mm")
                            .format(item.start_date!),
                        style: context.textTheme.titleLarge!
                            .copyWith(color: AppColors.white),
                      ),
                      const Gap(6),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Column(
                            children: [
                              CachedNetworkImage(
                                imageUrl: item.main_team.image,
                                width: 60,
                                height: 60,
                              ),
                              const Gap(4),
                              Text(
                                item.main_team.name,
                                style: context.textTheme.titleLarge!
                                    .copyWith(color: AppColors.white),
                              )
                            ],
                          ),
                          const Gap(10),
                          Column(
                            children: [
                              CachedNetworkImage(
                                imageUrl: item.second_team.image,
                                width: 60,
                                height: 60,
                              ),
                              const Gap(4),
                              Text(
                                item.second_team.name,
                                style: context.textTheme.titleLarge!
                                    .copyWith(color: AppColors.white),
                              )
                            ],
                          )
                        ],
                      )
                    ],
                  ),
                ),
                if (state.selectedMatch == item)
                  Container(
                    width: double.maxFinite,
                    height: 155,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        color: AppColors.black.withOpacity(0.4)),
                    child: const Icon(
                      Icons.check,
                      color: AppColors.white,
                      size: 80,
                    ),
                  )
              ],
            ),
          ),
        );
      },
    );
  }
}
