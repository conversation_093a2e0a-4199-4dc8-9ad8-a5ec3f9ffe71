import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/common/widgets/w_error.dart';
import 'package:echipta/features/common/widgets/w_text_field.dart';
import 'package:echipta/features/gift/presentation/bloc/gift_bloc.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';

class GiftScreen extends StatefulWidget {
  const GiftScreen({super.key});

  @override
  State<GiftScreen> createState() => _GiftScreenState();
}

class _GiftScreenState extends State<GiftScreen> {
  final TextEditingController controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.read<GiftBloc>()
      ..add(GetActiveMatchesEvent())
      ..add(const SearchClientsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        context.read<GiftBloc>().add(const SelectUserEvent());
        context.read<GiftBloc>().add(const SearchClientsEvent());
        return Future.value(true);
      },
      child: Scaffold(
        appBar: AppBar(title: Text(LocaleKeys.gift.tr(), style: context.textTheme.displaySmall)),
        body: Padding(
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
          child: Column(
            children: [
              Text(LocaleKeys.enterId.tr(), style: context.textTheme.bodyLarge!.copyWith(color: AppColors.darkGrey)),
              const Gap(20),
              WTextField(
                onChanged: (value) {
                  context.read<GiftBloc>().add(SearchClientsEvent(id: int.tryParse(value)));
                },
                controller: controller,
                isEmpty: controller.text.isEmpty,
                labelText: LocaleKeys.recieverId.tr(),
                prefixIcon: Icons.sensor_occupied_rounded,
              ),
              const Gap(10),
              BlocBuilder<GiftBloc, GiftState>(
                builder: (context, state) {
                  if (state.clientsStatus.isInProgress) {
                    return Center(
                      child: SizedBox(
                          height: 500,
                          width: 500,
                          child: Center(
                              child: CircularProgressIndicator.adaptive())),
                    );
                  } else if (state.clientsStatus.isFailure) {
                    return WError();
                  } else {
                    return Expanded(
                      child: ListView.separated(
                        padding: const EdgeInsets.only(top: 20),
                        itemCount: state.clients.length,
                        separatorBuilder: (context, index) => const Gap(10),
                        itemBuilder: (context, index) {
                          final item = state.clients[index];
                          return GestureDetector(
                            onTap: () {
                              context.read<GiftBloc>().add(SelectUserEvent(item: item));
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Row(
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(100),
                                    child: CachedNetworkImage(
                                      imageUrl: item.picture,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.cover,
                                      progressIndicatorBuilder: (context, url, progress) {
                                        return SizedBox(child: const CupertinoActivityIndicator());
                                      },
                                      errorWidget: (context, url, error) => Padding(
                                        padding: const EdgeInsets.all(15.0),
                                        child: CircleAvatar(
                                            backgroundColor: AppColors.primary2,
                                            child: const Icon(Icons.person, color: Colors.white)),
                                      ),
                                    ),
                                  ),
                                  const Gap(16),
                                  Flexible(child: Text(item.full_name, style: context.textTheme.bodyLarge)),
                                  const Spacer(),
                                  if (state.selectedUser == item) ...[
                                    const Icon(Icons.check_box, color: AppColors.primary),
                                    const Gap(20),
                                  ],
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
        bottomNavigationBar: BlocBuilder<GiftBloc, GiftState>(
          builder: (context, state) {
            return Padding(
              padding: EdgeInsets.fromLTRB(20, 0, 20, context.padding.bottom),
              child: WButton(
                onTap: () {
                  if (state.selectedUser != const UserEntity()) {
                    context.push(AppRouter.selectMatchScreen);
                  }
                },
                btnColor: state.selectedUser != const UserEntity() ? AppColors.primary : AppColors.mediumGrey,
                txtColor: state.selectedUser != const UserEntity() ? AppColors.white : AppColors.black,
                txt: LocaleKeys.selectGame.tr(),
              ),
            );
          },
        ),
      ),
    );
  }
}
