import 'package:bloc/bloc.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/gift/domain/usecases/active_matches_use_case.dart';
import 'package:echipta/features/gift/domain/usecases/search_client_use_case.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';
import 'package:equatable/equatable.dart';
import 'package:formz/formz.dart';

part 'gift_event.dart';
part 'gift_state.dart';

class GiftBloc extends Bloc<GiftEvent, GiftState> {
  final SearchClientUseCase _clientUseCase = SearchClientUseCase();
  final ActiveMatchesUseCase _activeMatchesUseCase = ActiveMatchesUseCase();
  GiftBloc() : super(GiftInitial()) {
    on<SearchClientsEvent>((event, emit) async {
      emit(state.copyWith(clientsStatus: FormzSubmissionStatus.inProgress));
      final params = IdParam(id: event.id!);
      final result = await _clientUseCase.call(params);
      if (result.isRight) {
        emit(state.copyWith(
            clients: result.right,
            clientsStatus: FormzSubmissionStatus.success));
      } else {
        emit(state.copyWith(clientsStatus: FormzSubmissionStatus.failure));
      }
    });
    on<SelectUserEvent>((event, emit) {
      emit(state.copyWith(selectedUser: event.item));
    });
    on<GetActiveMatchesEvent>((event, emit) async {
      emit(state.copyWith(
          activeMatchesStatus: FormzSubmissionStatus.inProgress));
      final result = await _activeMatchesUseCase.call(NoParams());
      if (result.isRight) {
        emit(state.copyWith(
            activeMatches: result.right,
            activeMatchesStatus: FormzSubmissionStatus.success));
      } else {
        emit(
            state.copyWith(activeMatchesStatus: FormzSubmissionStatus.failure));
      }
    });
    on<SelectMatchForGiftEvent>((event, emit) {
      emit(state.copyWith(selectedMatch: event.item));
    });
  }
}
