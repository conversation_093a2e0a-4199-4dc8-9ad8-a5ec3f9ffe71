// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'gift_bloc.dart';

class GiftState extends Equatable {
  const GiftState({
    this.clients = const [],
    this.clientsStatus = FormzSubmissionStatus.initial,
    this.selectedUser = const UserEntity(),
    this.activeMatches = const [],
    this.selectedMatch = const MatchEntity(),
    this.activeMatchesStatus = FormzSubmissionStatus.initial,
    this.paymentStats = FormzSubmissionStatus.initial,
  });

  final List<UserEntity> clients;
  final FormzSubmissionStatus clientsStatus;
  final UserEntity selectedUser;
  final List<MatchEntity> activeMatches;
  final FormzSubmissionStatus activeMatchesStatus;
  final MatchEntity selectedMatch;
  final FormzSubmissionStatus paymentStats;
  @override
  List<Object> get props => [
        clients,
        clientsStatus,
        selectedUser,
        activeMatches,
        selectedMatch,
        activeMatchesStatus,
        paymentStats,
      ];

  GiftState copyWith({
    List<UserEntity>? clients,
    FormzSubmissionStatus? clientsStatus,
    UserEntity? selectedUser,
    List<MatchEntity>? activeMatches,
    FormzSubmissionStatus? activeMatchesStatus,
    MatchEntity? selectedMatch,
    FormzSubmissionStatus? paymentStats,
  }) {
    return GiftState(
      clients: clients ?? this.clients,
      clientsStatus: clientsStatus ?? this.clientsStatus,
      selectedUser: selectedUser ?? this.selectedUser,
      activeMatches: activeMatches ?? this.activeMatches,
      activeMatchesStatus: activeMatchesStatus ?? this.activeMatchesStatus,
      selectedMatch: selectedMatch ?? this.selectedMatch,
      paymentStats: paymentStats ?? this.paymentStats,
    );
  }
}

final class GiftInitial extends GiftState {}
