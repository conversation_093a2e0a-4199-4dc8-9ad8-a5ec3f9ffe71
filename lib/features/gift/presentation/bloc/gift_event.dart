part of 'gift_bloc.dart';

sealed class GiftEvent extends Equatable {
  const GiftEvent();

  @override
  List<Object> get props => [];
}

class SearchClientsEvent extends GiftEvent {
  final int? id;
  const SearchClientsEvent({this.id = -1});
}

class SelectUserEvent extends GiftEvent {
  final UserEntity? item;

  const SelectUserEvent({this.item = const UserEntity()});
}

class GetActiveMatchesEvent extends GiftEvent {}

class SelectMatchForGiftEvent extends GiftEvent {
  final MatchEntity item;

  const SelectMatchForGiftEvent(this.item);
}

class PayForGiftEvent extends GiftEvent {
  final Function(String) onSucces;
  final Function(String) onError;
  const PayForGiftEvent({required this.onSucces, required this.onError});
}
