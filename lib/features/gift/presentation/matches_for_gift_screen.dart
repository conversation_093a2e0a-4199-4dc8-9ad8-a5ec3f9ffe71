// ignore_for_file: deprecated_member_use

import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/features/gift/presentation/bloc/gift_bloc.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/gift/presentation/widgets/w_match_item_for_gift.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';

class MatchesForGiftScreen extends StatelessWidget {
  const MatchesForGiftScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocaleKeys.selectGame2.tr(),
              style: context.textTheme.displayLarge,
            ),
            const Gap(16),
            Text(
              LocaleKeys.selectMatch.tr(),
              style: context.textTheme.bodyLarge!.copyWith(
                color: AppColors.darkGrey,
              ),
            ),
            const Gap(20),
            Expanded(
              child: BlocBuilder<GiftBloc, GiftState>(
                builder: (context, state) {
                  return GridView.builder(
                    itemCount: state.activeMatches.length,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          mainAxisExtent: 130,
                          mainAxisSpacing: 10,
                          crossAxisSpacing: 10,
                        ),
                    itemBuilder: (context, index) {
                      final item = state.activeMatches[index];
                      return WMatchItemForGift(item: item, index: index);
                    },
                  );
                },
              ),
            ),
            // const Spacer(),
            const Gap(20),
            BlocBuilder<GiftBloc, GiftState>(
              builder: (context, giftState) {
                return BlocBuilder<OrderBloc, OrderState>(
                  builder: (context, orderState) {
                    return WButton(
                      onTap: () {
                        if (giftState.selectedMatch != const MatchEntity()) {
                          context.read<OrderBloc>().add(
                            const SelectOrderTypeEvent(orderType: OrderType.gift),
                          );
                          context.push(
                            AppRouter.sector,
                            extra: orderState.selectedMatch,
                          );
                          // Clear both GiftBloc and OrderBloc selected match states
                          // context.read<GiftBloc>().add(ClearSelectedMatchEvent());
                          // context.read<OrderBloc>().add(SelectMatchEvent(match: const MatchEntity()));
                        }
                      },
                      btnColor:
                          giftState.selectedMatch != const MatchEntity()
                              ? AppColors.primary
                              : AppColors.mediumGrey,
                      txtColor:
                          giftState.selectedMatch != const MatchEntity()
                              ? AppColors.white
                              : AppColors.black,
                      txt: LocaleKeys.confirm.tr(),
                    );
                  },
                );
              },
            ),
            Gap(context.padding.bottom),
          ],
        ),
      ),
    );
  }
}
