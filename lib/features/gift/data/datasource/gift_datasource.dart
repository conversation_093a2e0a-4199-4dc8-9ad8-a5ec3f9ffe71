import 'package:dio/dio.dart';
import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/common/models/error_model.dart';
import 'package:echipta/features/home/<USER>/models/match_model.dart';
import 'package:echipta/features/profile/data/models/user_model.dart';

abstract class GiftDatasource {
  Future<List<UserModel>> searchClient(IdParam params);
  Future<List<MatchModel>> getActiveMatches();
  Future<String?> giftPayment(OrderParams params);
}

class GiftDatasourceImpl implements GiftDatasource {
  final Dio _dio = serviceLocator<DioSettings>().dio;

  @override
  Future<List<UserModel>> searchClient(IdParam params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      String param = "";
      if (params.id != -1) {
        param = "?id=${params.id}";
      }
      final response = await _dio.get("/gift/search-client$param",
          options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List)
            .map((e) => UserModel.fromJson(e as Map<String, dynamic>))
            .toList();
      } else {
        final message = (response.data as Map<String, dynamic>)
            .values
            .toString()
            .replaceAll(
              RegExp(r'[\[\]]'),
              '',
            );
        throw CustomException(
          message: message,
          code: '${response.statusCode}',
        );
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<List<MatchModel>> getActiveMatches() async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final response = await _dio.get("/gift/get-active-games",
          options: Options(headers: {"Authorization": token}));
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        return (response.data["data"] as List)
            .map((e) => MatchModel.fromJson(e as Map<String, dynamic>))
            .toList();
      } else {
        final message = (response.data as Map<String, dynamic>)
            .values
            .toString()
            .replaceAll(
              RegExp(r'[\[\]]'),
              '',
            );
        throw CustomException(
          message: message,
          code: '${response.statusCode}',
        );
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }

  @override
  Future<String?> giftPayment(OrderParams params) async {
    try {
      final token = StorageRepository.getString(StoreKeys.token);
      final data = {
        "match_id": params.matchId,
        "sector": params.sector,
        "row": params.row,
        "seat": params.seat,
        "payment_type": params.paymentType,
        "gift_to_user": params.userForGift
      };
      final response = await _dio.post(
        "/games/ticket-payment",
        data: data,
        options: Options(headers: {"Authorization": token}),
      );
      if (response.statusCode! >= 200 && response.statusCode! < 300) {
        if (response.data["data"].isEmpty) {
          return null;
        } else {
          return response.data["data"]["payment_url"];
        }
      } else {
        if (response.data is Map<String, dynamic>) {
          final error = ErrorModel.fromJson(response.data);
          if (error.errors.isNotEmpty) {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: error.errors.first.message,
                errorKey: error.errors.first.error);
          } else {
            throw ServerException(
                statusCode: response.statusCode ?? 500,
                errorMessage: response.data.toString(),
                errorKey: error.errors.first.error);
          }
        } else {
          throw ServerException(
              statusCode: response.statusCode ?? 500,
              errorMessage: response.data.toString(),
              errorKey: "Unknown error");
        }
      }
    } on DioException catch (error) {
      throw DioException(requestOptions: error.requestOptions);
    } on CustomException {
      rethrow;
    } on ServerException {
      rethrow;
    } on Exception catch (error) {
      throw CustomException(message: '$error', code: '141');
    }
  }
}
