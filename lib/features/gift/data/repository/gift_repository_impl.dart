import 'package:dio/dio.dart';
import 'package:echipta/core/exceptions/custom_exception.dart';
import 'package:echipta/core/exceptions/exceptions.dart';
import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/gift/data/datasource/gift_datasource.dart';
import 'package:echipta/features/gift/domain/repository/gift_repository.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';

class GiftRepositoryImpl implements GiftRepository {
  final GiftDatasource _datasource = serviceLocator<GiftDatasourceImpl>();
  @override
  Future<Either<Failure, List<UserEntity>>> searchClient(IdParam params) async {
    try {
      final result = await _datasource.searchClient(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey));
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, List<MatchEntity>>> getActiveMatches() async {
    try {
      final result = await _datasource.getActiveMatches();
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey));
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }

  @override
  Future<Either<Failure, String?>> giftPayment(OrderParams params) async {
    try {
      final result = await _datasource.giftPayment(params);
      return Right(result);
    } on CustomException catch (e) {
      return Left(CustomException(message: e.message, code: e.code));
    } on ServerException catch (e) {
      return Left(ServerFailure(
          statusCode: e.statusCode,
          errorMessage: e.errorMessage,
          errorKey: e.errorKey));
    } on ParsingException catch (e) {
      return Left(ParsingFailure(errorMessage: e.errorMessage));
    } on DioException {
      return Left(DioFailure());
    }
  }
}
