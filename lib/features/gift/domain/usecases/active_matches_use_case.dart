import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/gift/data/repository/gift_repository_impl.dart';
import 'package:echipta/features/gift/domain/repository/gift_repository.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';

class ActiveMatchesUseCase extends UseCase<List<MatchEntity>, NoParams> {
  final GiftRepository _repository = serviceLocator<GiftRepositoryImpl>();

  @override
  Future<Either<Failure, List<MatchEntity>>> call(NoParams params) async {
    return await _repository.getActiveMatches();
  }
}
