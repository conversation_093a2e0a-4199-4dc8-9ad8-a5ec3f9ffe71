import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/gift/data/repository/gift_repository_impl.dart';
import 'package:echipta/features/gift/domain/repository/gift_repository.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';

class SearchClientUseCase extends UseCase<List<UserEntity>, IdParam> {
  final GiftRepository _repository = serviceLocator<GiftRepositoryImpl>();

  @override
  Future<Either<Failure, List<UserEntity>>> call(IdParam params) async {
    return await _repository.searchClient(params);
  }
}
