import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/core/utils/params.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/profile/domain/entities/user_entity.dart';

abstract class GiftRepository {
  Future<Either<Failure, List<UserEntity>>> searchClient(IdParam params);
  Future<Either<Failure, List<MatchEntity>>> getActiveMatches();
  Future<Either<Failure, String?>> giftPayment(OrderParams params);
}
