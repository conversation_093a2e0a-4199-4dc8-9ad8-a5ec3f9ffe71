import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:echipta/assets/app_constants.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';

class DioSettings {
  late BaseOptions _dioBaseOptions;

  void setBaseOptions({String? lang}) {
    _dioBaseOptions = BaseOptions(
      baseUrl: AppConstants.forProduction ? AppConstants.baseUrlProd : AppConstants.baseUrlDev,
      connectTimeout: const Duration(milliseconds: 35000),
      receiveTimeout: const Duration(milliseconds: 33000),
      followRedirects: false,
      headers: {'Accept-Language': lang ?? StorageRepository.getString(StoreKeys.language, defValue: 'uz')},
      validateStatus: (status) => status != null && status <= 500,
    );
  }

  Dio get dio {
    final dio = Dio(_dioBaseOptions);
    if (!AppConstants.forProduction) {
      dio.interceptors.addAll([
        alice.getDioInterceptor(),
        LogInterceptor(
          responseBody: true,
          requestBody: true,
          request: true,
          requestHeader: true,
          logPrint: (object) => log(object.toString()),
        ),
      ]);
    }
    return dio;
  }
}
