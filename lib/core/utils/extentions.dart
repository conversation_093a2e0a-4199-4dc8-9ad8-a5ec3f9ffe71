import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

extension DateFormatExtension on String {
  String formatDate(String date) {
    try {
      final DateTime parsedDate = DateTime.parse(date);
      final DateFormat formatter = DateFormat(this);
      return formatter.format(parsedDate);
    } catch (e) {
      // Handle the error, maybe return an empty string or a default value
      return '';
    }
  }
}

extension TimeFormatter on int {
  String toMinutesSeconds() {
    int minutes = this ~/ 60;
    int seconds = this % 60;
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = seconds.toString().padLeft(2, '0');
    return '$formattedMinutes:$formattedSeconds';
  }
}

extension FormatNumber on double {
  String formatAsSpaceSeparated() {
    String formatted = toStringAsFixed(2).split(" ").join();
    List<String> parts = formatted.split('.');
    String wholePart = parts[0];

    final RegExp regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    wholePart = wholePart.replaceAllMapped(regex, (Match match) => "${match[1]} ");

    return wholePart;
  }
}

extension PhoneNumberFormatter on String {
  String formatPhoneNumber() {
    if (length != 12) {
      throw Exception("Phone number must be 12 digits long");
    }

    // Extract different parts of the phone number
    final countryCode = substring(0, 3); // "998"
    final areaCode = substring(3, 5); // "88"
    final part1 = substring(5, 8); // "472"
    final part2 = substring(8, 10); // "77"
    final part3 = substring(10, 12); // "72"

    return '+$countryCode $areaCode $part1 $part2 $part3';
  }
}

extension BuildContextExtensions on BuildContext {
  ThemeData get theme => Theme.of(this);

  TextTheme get textTheme => Theme.of(this).textTheme;

  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  EdgeInsets get padding => MediaQuery.paddingOf(this);

  Size get sizeOf => MediaQuery.sizeOf(this);

  Brightness get brightness => theme.brightness;

  AppBarTheme get appBarTheme => theme.appBarTheme;
}

extension StringExtension on String {
  String joinSymbols() {
    // Define the set of symbols to split
    final splitSymbols = {'(', ')', '-', ' '};
    // Split the string into individual characters, excluding symbols
    List<String> characters = [];
    for (int i = 0; i < length; i++) {
      if (!splitSymbols.contains(this[i])) {
        characters.add(this[i]);
      }
    }

    // Join the characters into a string
    return characters.join();
  }
}

extension FormatToMinutes on int {
  String formatTime() {
    int minutes = this ~/ 60; // Calculate minutes
    int remainingSeconds = this % 60; // Calculate remaining seconds
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}'; // Format as MM:SS
  }
}

extension DateTimeFormatter on String {
  /// Converts a string with format "dd.MM.yyyy HH:mm:ss" to "yyyy-MM-dd HH:mm:ss"
  String formatDateTime({
    String inputFormat = "dd.MM.yyyy HH:mm:ss",
    String outputFormat = "yyyy-MM-dd HH:mm:ss",
    required BuildContext context,
  }) {
    try {
      DateTime parsedDate = DateFormat(inputFormat).parse(this);
      return DateFormat(outputFormat, context.locale.languageCode).format(parsedDate);
    } catch (e) {
      return "Invalid date";
    }
  }
}

extension BuildContextExtension on BuildContext {
  void soonFeature() {
    showTopSnackBar(
      Overlay.of(this),
      animationDuration: Duration(milliseconds: 400),
      reverseAnimationDuration: Duration(milliseconds: 300),
      displayDuration: Duration(milliseconds: 1400),
      curve: Curves.easeInOut,

      CustomSnackBar.info(
        icon: SizedBox.shrink(),
        message: "Tez orada ishga tushiriladi",
        backgroundColor: AppColors.primary,
        textStyle: textTheme.displayMedium!.copyWith(color: AppColors.white, fontWeight: FontWeight.bold),
      ),
    );
  }
}
