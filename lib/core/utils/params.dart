// ignore_for_file: non_constant_identifier_names

import 'package:image_picker/image_picker.dart';

class AuthParams {
  final String phoneNumber;
  final String code;

  AuthParams({this.phoneNumber = "", this.code = ""});
}

class SectorParams {
  final String sector;
  final int match_id;
  SectorParams({required this.sector, required this.match_id});
}

class OrderParams {
  final int matchId;
  final String sector;
  final String row;
  final String seat;
  final String? paymentType;
  final String? userForGift;
  final String? type;
  final int? city;
  OrderParams({
    required this.matchId,
    required this.sector,
    required this.row,
    required this.seat,
    this.paymentType = "",
    this.userForGift,
    this.city = -1,
    this.type = "",
  });
}

class UserParams {
  final String fullName;
  final XFile? avatar;
  final String birthDate;

  UserParams({this.fullName = "", this.avatar, this.birthDate = ""});
}

class ChatParams {
  final String message;
  const ChatParams({required this.message});
}

class ProductPaymentParams {
  final List<Item> items;
  final String delivery_type;
  final String delivery_address;
  final String payment_type;

  ProductPaymentParams({
    required this.items,
    required this.delivery_type,
    required this.delivery_address,
    required this.payment_type,
  });
}

class Item {
  final int product_id;
  final int count;

  Item({required this.product_id, required this.count});
}

class IdParam {
  final int? id;

  IdParam({this.id = -1});
}

class IdCardParams {
  final String type;
  final String sector;
  final String row;
  final String seat;

  IdCardParams({
    required this.type,
    required this.sector,
    required this.row,
    required this.seat,
  });
}
