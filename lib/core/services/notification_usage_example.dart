import 'package:flutter/material.dart';
import 'package:echipta/core/services/notification_service.dart';

/// Example of how to use Enhanced NotificationService
///
/// This file shows various ways to use the unified notification service
/// with internet connectivity monitoring and Go Router navigation
class NotificationUsageExample {

  /// Show a simple notification
  static Future<void> showSimpleNotification() async {
    await NotificationService().showSimpleNotification(
      title: 'Test Notification',
      body: 'Bu test bildirishnomasi',
    );
  }

  /// Show notification with navigation data
  static Future<void> showTicketNotification() async {
    await NotificationService().showLocalNotification({
      'title': 'Chipta sotib olindi',
      'body': 'Sizning chiptangiz tayyor!',
      'type': 'ticket',
      'id': '123',
    });
  }

  /// Show order notification with navigation
  static Future<void> showOrderNotification() async {
    await NotificationService().showLocalNotification({
      'title': 'To\'lov holati o\'zgardi',
      'body': 'Buyurtmangiz holati yangilandi',
      'type': 'order',
      'id': '456',
    });
  }

  /// Show profile notification
  static Future<void> showProfileNotification() async {
    await NotificationService().showLocalNotification({
      'title': 'Profil yangilandi',
      'body': 'Profilingiz muvaffaqiyatli yangilandi',
      'type': 'profile',
    });
  }

  /// Get FCM token for server communication
  static Future<String?> getFCMToken() async {
    return NotificationService().fcmToken;
  }

  /// Check if notifications are allowed
  static Future<bool> checkPermissions() async {
    return await NotificationService().areNotificationsAllowed();
  }

  /// Request notification permissions
  static Future<bool> requestPermissions() async {
    return await NotificationService().requestPermissions();
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await NotificationService().cancelAllNotifications();
  }

  /// Reset badge counter
  static Future<void> resetBadge() async {
    await NotificationService().resetBadgeCounter();
  }
}

/// Example widget showing how to use notifications in UI
class NotificationTestWidget extends StatelessWidget {
  const NotificationTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notification Test')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: NotificationUsageExample.showSimpleNotification,
              child: const Text('Show Simple Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: NotificationUsageExample.showTicketNotification,
              child: const Text('Show Ticket Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: NotificationUsageExample.showOrderNotification,
              child: const Text('Show Order Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: NotificationUsageExample.showProfileNotification,
              child: const Text('Show Profile Notification'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final token = await NotificationUsageExample.getFCMToken();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('FCM Token: ${token ?? "null"}')),
                  );
                }
              },
              child: const Text('Get FCM Token'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final allowed = await NotificationUsageExample.checkPermissions();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Notifications allowed: $allowed')),
                  );
                }
              },
              child: const Text('Check Permissions'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: NotificationUsageExample.requestPermissions,
              child: const Text('Request Permissions'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: NotificationUsageExample.cancelAllNotifications,
              child: const Text('Cancel All Notifications'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: NotificationUsageExample.resetBadge,
              child: const Text('Reset Badge'),
            ),
          ],
        ),
      ),
    );
  }
}
