// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: prefer_single_quotes, avoid_renaming_method_parameters, constant_identifier_names

import 'dart:ui';

import 'package:easy_localization/easy_localization.dart' show AssetLoader;

class CodegenLoader extends AssetLoader{
  const CodegenLoader();

  @override
  Future<Map<String, dynamic>?> load(String path, Locale locale) {
    return Future.value(mapLocales[locale.toString()]);
  }

  static const Map<String,dynamic> _ru = {
  "confirm": "Подтвердить",
  "login": "Войти",
  "sendCode": "Отправить код",
  "enterCode": "Введите код",
  "resendCodeTime": "Отправить код повторно {time}",
  "resendCode": "Отправить повторно",
  "createAccount": "Создать аккаунт",
  "fio": "ФИО",
  "birthDate": "Дата рождения",
  "selectRegion": "Выберите ваш регион",
  "allRight": "Все верно",
  "regInfo": "Внимание: Убедитесь, что ваша информация правильная. Данные будут привязаны к вашему ID-карте",
  "singin": "Регистрация",
  "other": "Другое",
  "enterAddress": "Введите адрес",
  "selfieForIDCard": "Селфи для ID-карты",
  "selectBrightnessPlace": "Выберите более светлое место для селфи",
  "nearToCamera": "Подойдите ближе и смотрите в камеру",
  "takePicture": "Сделать фото",
  "confirmed": "Подтверждено",
  "welcome": "Добро пожаловать на платформу echipta!",
  "accountVerified": "Ваш профиль подтвержден",
  "enter": "Вход",
  "skip": "Пропустить",
  "setPinCode": "Установить код для приложения",
  "set": "Установить",
  "selectTeam": "Выберите вашу команду",
  "logout": "Выйти",
  "helloEnterPin": "Здравствуйте, введите код",
  "needHelp": "Нужна помощь",
  "resultsSearch": "Результаты поиска:",
  "search": "Поиск...",
  "main": "Главная",
  "rating": "Рейтинг",
  "help": "Помощь",
  "account": "Профиль",
  "currentMatch": "Текущий матч",
  "products": "Продукты",
  "support": "Служба поддержки",
  "enterMessage": "Введите сообщение...",
  "first": "Первый",
  "second": "Второй",
  "third": "Третий",
  "vip": "VIP",
  "fan": "Фанат",
  "guest": "Гость",
  "emptyPlaces": "Свободные места",
  "notEmptyPlaces": "Занятые места",
  "selected": "Выбрано",
  "validation": "Поле не должно быть пустым",
  "phoneNumber": "Номер телефона",
  "currentTicket": "Текущий билет",
  "started": "Началось",
  "finished": "ЗАВЕРШЕНО",
  "onSale": "БИЛЕТЫ В ПРОДАЖЕ",
  "somethingWentWrong": "Что-то не так, попробуйте еще раз",
  "fanRanking": "Рейтинг фанатов",
  "settings": "Настройки",
  "gift": "Подарить",
  "orderhistory": "История заказов",
  "myid": "Моя ID-карта",
  "mytickets": "Мои билеты",
  "privacyPolicy": "Политика конфиденциальности",
  "orderProduct": "Заказать",
  "price": "{price} сум",
  "noCurrentTicket": "У вас нет текущего билета",
  "deliveryAddress": "Адрес доставки: Андижанская область, массив Кушарик, стадион \"Бабур Арена,\" магазин \"Andijon shop\"",
  "commonPrice": "Общая цена:",
  "pay": "Оплата",
  "appPin": "Пин-код приложения",
  "appLang": "Язык приложения",
  "selectGame": "Выбрать игру",
  "recieverId": "ID-номер получателя",
  "enterId": "Введите ID пользователя, которого вы хотите отправить",
  "selectGame2": "Выберите игру",
  "selectMatch": "Выберите игру, в которой вы хотите подарить билеты другу",
  "selectSector": "Выбрать сектор",
  "sector": "Сектор",
  "type": "Тип",
  "emptySeats": "Количество свободных мест",
  "select": "Выбрать",
  "price2": "Цена:",
  "selectSeat": "Выбрать место",
  "emptySeats2": "Свободные места",
  "row": "Ряд",
  "seat": "Место",
  "idcardNumber": "Номер вашей ID-карты",
  "idcardNumberCopied": "Номер вашей ID-карты скопирован",
  "recieptIsChecking": "Ваш запрос проверяется",
  "error": "Произошла ошибка, пожалуйста, попробуйте снова через некоторое время!",
  "paymentInfo": "Информация о платеже",
  "game": "Игра",
  "date": "Дата",
  "ticketPrice": "Цена билета",
  "time": "Время покупки билета:",
  "paymentType": "Способ оплаты",
  "profileBalance": "Через баланс профиля",
  "alifPay": "Через Alif Pay",
  "endPay": "Завершить оплату",
  "reciever": "Получатель",
  "viewPlace": "Посмотреть место",
  "viziualView": "Визуальный вид",
  "pov": "Вид игры с места",
  "notification": "Уведомления",
  "mycards": "Мои карты",
  "profileBalance2": "Баланс профиля",
  "addCard": "Добавить карту",
  "paymentHistory": "История платежей",
  "topup": "Пополнить баланс",
  "currentMatchNotAvailable": "Текущий матч недоступен",
  "updateTitleRequired": "Требуется обновление",
  "updateTitleOptional": "Свежее обновление уже здесь!",
  "updateDescRequired": "Чтобы продолжить использование Echipta, обновите приложение до последней версии. Это обновление включает важные улучшения и исправления.",
  "updateDescOptional": "Мы улучшили приложение и добавили немного магии ✨! Обновите сейчас, чтобы насладиться новыми функциями и более плавной работой.",
  "cancelUpdate": "Напомнить позже",
  "update": "Обновить",
  "technicalWorksTitle": "Идут технические работы",
  "technicalWorksDesc": "Мы занимаемся техническими работами. Это может занять некоторое время, но не волнуйтесь.",
  "closeApp": "Закрыть"
};
static const Map<String,dynamic> _uz = {
  "confirm": "Tasdiqlash",
  "login": "Tizimga kirish",
  "sendCode": "Kodni yuborish",
  "enterCode": "Kodni kiriting",
  "resendCodeTime": "Kodni qayta yuborish {time}",
  "resendCode": "Qayta yuborish",
  "createAccount": "Hisob ochish",
  "fio": "Ism va familiya",
  "birthDate": "Tug‘ilgan sana",
  "selectRegion": "Hududingizni tanlang",
  "allRight": "Barchasi to‘g‘ri",
  "regInfo": "Diqqat ushbu ma‘lumotlaringiz to‘griligiga ishonch hosil qiling. Ma‘lumotlar ID kartangizga bog‘lanadi",
  "singin": "Ro‘yhatdan o‘tish",
  "other": "Boshqa",
  "enterAddress": "Manzil kiriting",
  "selfieForIDCard": "ID karta uchun selfi",
  "selectBrightnessPlace": "Selfi uchun yoruqroq joy tanlang",
  "nearToCamera": "Yaqinroq va kameraga qarang",
  "takePicture": "Rasmga olish",
  "confirmed": "Tasdiqlandi",
  "welcome": "Assalomu alaykum echipta platformasiga xush kelibsiz!",
  "accountVerified": "Profilingiz tasdiqlandi",
  "enter": "Kirish",
  "skip": "O‘tkazib yuborish",
  "setPinCode": "Ilovaga kod qo‘yish",
  "set": "O‘rnatish",
  "selectTeam": "Jamoangizni tanlang",
  "logout": "Chiqish",
  "helloEnterPin": "Assalomu alaykum kodni kiriting",
  "needHelp": "Yordam kerak",
  "resultsSearch": "Qidiruv natijalari:",
  "search": "Qidish",
  "main": "Asosiy",
  "rating": "Reyting",
  "help": "Yordam",
  "account": "Profil",
  "currentMatch": "Joriy o‘yin",
  "products": "Mahsulotlar",
  "support": "Yordam xizmati",
  "enterMessage": "Xabar kiriting...",
  "first": "Birinchi",
  "second": "Ikkinchi",
  "third": "Uchinchi",
  "vip": "VIP",
  "fan": "FAN",
  "guest": "Mehmon",
  "emptyPlaces": "Bo‘sh joylar",
  "notEmptyPlaces": "Band joylar",
  "selected": "Tanlangan",
  "validation": "Maydon bo‘sh bo‘lmasligi kerak",
  "phoneNumber": "Telefon raqami",
  "currentTicket": "Joriy chipta",
  "started": "BOSHLANDI",
  "finished": "YAKUNLANDI",
  "onSale": "CHIPTA SOTUVDA",
  "somethingWentWrong": "Nimadir xato ketdi, iltimos qayta urinib ko‘ring",
  "fanRanking": "Muxlislar reytingi",
  "settings": "Sozlamalar",
  "gift": "Sovg‘a qilish",
  "orderhistory": "Buyurtmalar tarixi",
  "myid": "Id Kartam",
  "mytickets": "Chiptalarim",
  "privacyPolicy": "Maxfiylik siyosati",
  "orderProduct": "Buyurtma qilish",
  "price": "{price} so‘m",
  "noCurrentTicket": "Sizda joriy chipta mavjud emas",
  "deliveryAddress": "Olib ketish manzili: Andijon viloyati, Qo`shariq dahasi Bobur arena o`yingohi, Andijon shop dokoni",
  "commonPrice": "Jami narx",
  "pay": "To‘lov qilish",
  "appPin": "Ilova pinkodi",
  "appLang": "Ilova tili",
  "selectGame": "O‘yin tanlash",
  "recieverId": "Qabul qiluvchi ID raqami",
  "enterId": "Yubormoqchi bo‘lgan foydalanuvchingiz ID raqamini kiriting",
  "selectGame2": "O‘yin tanlang",
  "selectMatch": "Do‘stingizga chipta sovg`a qilmoqchi bo`lgan o‘yiningizni tanlang",
  "selectSector": "Sektor tanlash",
  "sector": "Sektor",
  "type": "Toifa",
  "emptySeats": "Bosh joylar soni",
  "select": "Tanlash",
  "price2": "Narxi:",
  "selectSeat": "Joy tanlash",
  "emptySeats2": "Bo‘sh joylar",
  "row": "Qator",
  "seat": "Joy",
  "idcardNumber": "ID kartangiz raqami",
  "idcardNumberCopied": "ID kartangiz raqami nusxalandi",
  "recieptIsChecking": "Arizangiz ko'rib chiqilmoqda",
  "error": "Xatolik yuz berdi, iltimos bir ozdan so'ng qayta urinib ko'ring!",
  "paymentInfo": "To‘lov tafsilotlari",
  "game": "O‘yin",
  "date": "Sana",
  "ticketPrice": "Chipta narxi",
  "time": "Chipta sotib olish vaqti:",
  "paymentType": "To‘lov usuli",
  "profileBalance": "Profil balansi orqali",
  "alifPay": "Alif Pay orqali",
  "endPay": "To‘lovni amalga oshirish",
  "reciever": "Qabul qiluvchi",
  "viewPlace": "Joyni ko'rish",
  "viziualView": "Vizual ko'rinish",
  "pov": "Joydan o‘yin ko‘rinishi",
  "notification": "Bildirishnomalar",
  "mycards": "Kartalarim",
  "profileBalance2": "Profil balansi",
  "addCard": "Karta qo'shish",
  "paymentHistory": "To'lov tarixi",
  "topup": "Balansni to'ldirish",
  "orderHistory": "Buyurtmalar tarixi",
  "tickets": "Chiptalar",
  "additionalInfo": "Qo'shimcha ma'lumot",
  "thereEmpty": "Bu yer hozircha bo'sh",
  "youdonthaveid": "Sizda ID karta mavjud emas",
  "currentMatchNotAvailable": "Joriy o'yin mavjud emas",
  "updateTitleRequired": " Ilovani yangilash talab qilinadi",
  "updateTitleOptional": "Yangilanish tayyor!",
  "updateDescRequired": "Echipta ilovasidan foydalanishni davom ettirish uchun uni eng so‘nggi versiyaga yangilashingiz kerak. Ushbu yangilanish muhim yaxshilashlar va tuzatishlarni o‘z ichiga oladi.",
  "updateDescOptional": "Biz ba'zi yaxshilashlar va ozgina sehr qo‘shdik ✨! Yangi funksiyalar va yanada silliq tajribadan bahramand bo‘lish uchun ilovani yangilang.",
  "cancelUpdate": "Keyinroq eslatish",
  "update": "Yangilash",
  "technicalWorksTitle": "Texnik ishlar davom etmoqda",
  "technicalWorksDesc": "Biz texnik ishlar bilan shug‘ullanmoqdamiz. Bu biroz vaqt olishi mumkin, lekin xavotir olmang.",
  "closeApp": "Yopish"
};
static const Map<String, Map<String,dynamic>> mapLocales = {"ru": _ru, "uz": _uz};
}
